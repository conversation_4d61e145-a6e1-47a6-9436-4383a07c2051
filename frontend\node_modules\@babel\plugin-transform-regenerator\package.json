{"name": "@babel/plugin-transform-regenerator", "author": "The Babel Team (https://babel.dev/team)", "description": "Explode async and generator functions into a state machine.", "version": "7.20.5", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-regenerator"}, "main": "./lib/index.js", "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "regenerator-transform": "^0.15.1"}, "license": "MIT", "publishConfig": {"access": "public"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.20.5", "@babel/helper-plugin-test-runner": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "type": "commonjs"}