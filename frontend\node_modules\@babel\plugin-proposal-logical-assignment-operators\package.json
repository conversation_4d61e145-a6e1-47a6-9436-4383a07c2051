{"name": "@babel/plugin-proposal-logical-assignment-operators", "version": "7.20.7", "description": "Transforms logical assignment operators into short-circuited assignments", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-logical-assignment-operators"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.20.7", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-logical-assignment-operators", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}