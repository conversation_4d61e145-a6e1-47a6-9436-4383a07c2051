{"name": "@types/eslint", "version": "8.21.1", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j-f1", "githubUsername": "j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK", "githubUsername": "JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher", "githubUsername": "brad<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "cca49eb72557cdfcd45521e5f29108fd935fdc15936071968d79a0e7fae5c7f6", "typeScriptVersion": "4.2", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}}