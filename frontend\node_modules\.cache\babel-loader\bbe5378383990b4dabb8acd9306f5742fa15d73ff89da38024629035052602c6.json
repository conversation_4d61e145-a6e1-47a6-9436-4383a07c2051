{"ast": null, "code": "var _jsxFileName = \"D:\\\\CHUONGTRINHHOC\\\\DATN\\\\Python-KienTap-\\\\frontend\\\\src\\\\index.js\";\nimport React from \"react\";\nimport ReactDOM from \"react-dom/client\";\nimport \"./index.css\";\nimport App from \"./App\";\nimport reportWebVitals from \"./reportWebVitals\";\nimport \"./bootstrap.min.css\";\nimport { HashRouter } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst root = ReactDOM.createRoot(document.getElementById(\"root\"));\nroot.render( /*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(HashRouter, {\n    children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 11,\n  columnNumber: 3\n}, this));\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "reportWebVitals", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["D:/CHUONGTRINHHOC/DATN/Python-KienTap-/frontend/src/index.js"], "sourcesContent": ["import React from \"react\";\r\nimport ReactDOM from \"react-dom/client\";\r\nimport \"./index.css\";\r\nimport App from \"./App\";\r\nimport reportWebVitals from \"./reportWebVitals\";\r\nimport \"./bootstrap.min.css\";\r\nimport { HashRouter } from \"react-router-dom\";\r\n\r\nconst root = ReactDOM.createRoot(document.getElementById(\"root\"));\r\nroot.render(\r\n  <React.StrictMode>\r\n    <HashRouter>\r\n      <App />\r\n    </HashRouter>\r\n  </React.StrictMode>\r\n);\r\n\r\n// If you want to start measuring performance in your app, pass a function\r\n// to log results (for example: reportWebVitals(console.log))\r\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\r\nreportWebVitals();\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAO,aAAa;AACpB,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAO,qBAAqB;AAC5B,SAASC,UAAU,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,IAAI,GAAGN,QAAQ,CAACO,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;AACjEH,IAAI,CAACI,MAAM,eACTL,OAAA,CAACN,KAAK,CAACY,UAAU;EAAAC,QAAA,eACfP,OAAA,CAACF,UAAU;IAAAS,QAAA,eACTP,OAAA,CAACJ,GAAG;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAG;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA;AACI;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,QACI,CACpB;;AAED;AACA;AACA;AACAd,eAAe,EAAE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}