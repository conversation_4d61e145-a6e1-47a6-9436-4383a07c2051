{"ast": null, "code": "var _jsxFileName = \"D:\\\\CHUONGTRINHHOC\\\\DATN\\\\Python-KienTap-\\\\frontend\\\\src\\\\context\\\\productsContext.js\",\n  _s = $RefreshSig$();\nimport { createContext, useState } from \"react\";\nimport httpService from \"../services/httpService\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductsContext = /*#__PURE__*/createContext();\nexport default ProductsContext;\nexport const ProductsProvider = _ref => {\n  _s();\n  let {\n    children\n  } = _ref;\n  const [productsLoaded, setProductsLoaded] = useState(false);\n  const [products, setProducts] = useState([]);\n  const [error, setError] = useState(\"\");\n  const [brands, setBrands] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const loadProducts = async function () {\n    let forced = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    if (productsLoaded && !forced) return;\n    try {\n      const {\n        data\n      } = await httpService.get(\"/api/products/\");\n      setProducts(data);\n      const {\n        data: brandsData\n      } = await httpService.get(\"/api/brands/\");\n      setBrands(brandsData);\n      const {\n        data: categoriesData\n      } = await httpService.get(\"/api/category/\");\n      setCategories(categoriesData);\n      setError(\"\");\n    } catch (ex) {\n      setError(ex.message);\n    }\n    setProductsLoaded(true);\n  };\n  const loadProduct = async id => {\n    if (productsLoaded) {\n      const product = products.find(p => p.id == id);\n      return product;\n    }\n    try {\n      const {\n        data\n      } = await httpService.get(`/api/products/${id}/`);\n      return data;\n    } catch (ex) {\n      setError(ex.message);\n      return {};\n    }\n  };\n  const contextData = {\n    products,\n    error,\n    loadProducts,\n    loadProduct,\n    // productsLoaded,\n    brands,\n    categories\n  };\n  return /*#__PURE__*/_jsxDEV(ProductsContext.Provider, {\n    value: contextData,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductsProvider, \"dVjDGeR8jBlRvBwvdlEuurxcSGo=\");\n_c = ProductsProvider;\nvar _c;\n$RefreshReg$(_c, \"ProductsProvider\");", "map": {"version": 3, "names": ["createContext", "useState", "httpService", "jsxDEV", "_jsxDEV", "ProductsContext", "ProductsProvider", "_ref", "_s", "children", "productsLoaded", "setProductsLoaded", "products", "setProducts", "error", "setError", "brands", "setBrands", "categories", "setCategories", "loadProducts", "forced", "arguments", "length", "undefined", "data", "get", "brandsData", "categoriesData", "ex", "message", "loadProduct", "id", "product", "find", "p", "contextData", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/CHUONGTRINHHOC/DATN/Python-KienTap-/frontend/src/context/productsContext.js"], "sourcesContent": ["import { createContext, useState } from \"react\";\r\nimport httpService from \"../services/httpService\";\r\n\r\nconst ProductsContext = createContext();\r\n\r\nexport default ProductsContext;\r\n\r\nexport const ProductsProvider = ({ children }) => {\r\n  const [productsLoaded, setProductsLoaded] = useState(false);\r\n  const [products, setProducts] = useState([]);\r\n  const [error, setError] = useState(\"\");\r\n  const [brands, setBrands] = useState([]);\r\n  const [categories, setCategories] = useState([]);\r\n\r\n  const loadProducts = async (forced = false) => {\r\n    if (productsLoaded && !forced) return;\r\n\r\n    try {\r\n      const { data } = await httpService.get(\"/api/products/\");\r\n      setProducts(data);\r\n      const { data: brandsData } = await httpService.get(\"/api/brands/\");\r\n      setBrands(brandsData);\r\n      const { data: categoriesData } = await httpService.get(\"/api/category/\");\r\n      setCategories(categoriesData);\r\n      setError(\"\");\r\n    } catch (ex) {\r\n      setError(ex.message);\r\n    }\r\n\r\n    setProductsLoaded(true);\r\n  };\r\n\r\n  const loadProduct = async (id) => {\r\n    if (productsLoaded) {\r\n      const product = products.find((p) => p.id == id);\r\n      return product;\r\n    }\r\n\r\n    try {\r\n      const { data } = await httpService.get(`/api/products/${id}/`);\r\n      return data;\r\n    } catch (ex) {\r\n      setError(ex.message);\r\n      return {};\r\n    }\r\n  };\r\n\r\n  const contextData = {\r\n    products,\r\n    error,\r\n    loadProducts,\r\n    loadProduct,\r\n    // productsLoaded,\r\n    brands,\r\n    categories,\r\n  };\r\n\r\n  return (\r\n    <ProductsContext.Provider value={contextData}>\r\n      {children}\r\n    </ProductsContext.Provider>\r\n  );\r\n};\r\n"], "mappings": ";;AAAA,SAASA,aAAa,EAAEC,QAAQ,QAAQ,OAAO;AAC/C,OAAOC,WAAW,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,eAAe,gBAAGL,aAAa,EAAE;AAEvC,eAAeK,eAAe;AAE9B,OAAO,MAAMC,gBAAgB,GAAGC,IAAA,IAAkB;EAAAC,EAAA;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAF,IAAA;EAC3C,MAAM,CAACG,cAAc,EAAEC,iBAAiB,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACe,MAAM,EAAEC,SAAS,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAMmB,YAAY,GAAG,eAAAA,CAAA,EAA0B;IAAA,IAAnBC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IACxC,IAAIZ,cAAc,IAAI,CAACW,MAAM,EAAE;IAE/B,IAAI;MACF,MAAM;QAAEI;MAAK,CAAC,GAAG,MAAMvB,WAAW,CAACwB,GAAG,CAAC,gBAAgB,CAAC;MACxDb,WAAW,CAACY,IAAI,CAAC;MACjB,MAAM;QAAEA,IAAI,EAAEE;MAAW,CAAC,GAAG,MAAMzB,WAAW,CAACwB,GAAG,CAAC,cAAc,CAAC;MAClET,SAAS,CAACU,UAAU,CAAC;MACrB,MAAM;QAAEF,IAAI,EAAEG;MAAe,CAAC,GAAG,MAAM1B,WAAW,CAACwB,GAAG,CAAC,gBAAgB,CAAC;MACxEP,aAAa,CAACS,cAAc,CAAC;MAC7Bb,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOc,EAAE,EAAE;MACXd,QAAQ,CAACc,EAAE,CAACC,OAAO,CAAC;IACtB;IAEAnB,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMoB,WAAW,GAAG,MAAOC,EAAE,IAAK;IAChC,IAAItB,cAAc,EAAE;MAClB,MAAMuB,OAAO,GAAGrB,QAAQ,CAACsB,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACH,EAAE,IAAIA,EAAE,CAAC;MAChD,OAAOC,OAAO;IAChB;IAEA,IAAI;MACF,MAAM;QAAER;MAAK,CAAC,GAAG,MAAMvB,WAAW,CAACwB,GAAG,CAAE,iBAAgBM,EAAG,GAAE,CAAC;MAC9D,OAAOP,IAAI;IACb,CAAC,CAAC,OAAOI,EAAE,EAAE;MACXd,QAAQ,CAACc,EAAE,CAACC,OAAO,CAAC;MACpB,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,MAAMM,WAAW,GAAG;IAClBxB,QAAQ;IACRE,KAAK;IACLM,YAAY;IACZW,WAAW;IACX;IACAf,MAAM;IACNE;EACF,CAAC;EAED,oBACEd,OAAA,CAACC,eAAe,CAACgC,QAAQ;IAACC,KAAK,EAAEF,WAAY;IAAA3B,QAAA,EAC1CA;EAAQ;IAAA8B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACgB;AAE/B,CAAC;AAAClC,EAAA,CAvDWF,gBAAgB;AAAAqC,EAAA,GAAhBrC,gBAAgB;AAAA,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}