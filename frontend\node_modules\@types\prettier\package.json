{"name": "@types/prettier", "version": "2.7.2", "description": "TypeScript definitions for prettier", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/prettier", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/ikatyang", "githubUsername": "ikatyang"}, {"name": "<PERSON><PERSON><PERSON> Jr.", "url": "https://github.com/ifiokjr", "githubUsername": "ifiokjr"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ffflorian", "githubUsername": "f<PERSON>lorian"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/sosuke<PERSON>zuki", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/Shinigami92", "githubUsername": "Shinigami92"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/thorn0", "githubUsername": "thorn0"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/shian15810", "githubUsername": "shian15810"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/prettier"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "99cc9780c4abc9fa045f57796c8ae3ec7eee54669b1eecb6f4cc5ed62321a8a8", "typeScriptVersion": "4.2"}