import React, { useState, useContext, useEffect } from "react";
import ProductsContext from "../context/productsContext";
import UserContext from "../context/userContext";
import Message from "./message";
import Rating from "./rating";
import httpService from "../services/httpService";
import { Form, ListGroup, Button } from "react-bootstrap";
import { Link } from "react-router-dom";

function ReviewsList({ product }) {
  const [reviews, setReviews] = useState(
    product && product.reviews ? product.reviews : []
  );
  const [rating, setRating] = useState("");
  const [error, setError] = useState("");
  const [comment, setComment] = useState("");
  const [reviewPermission, setReviewPermission] = useState({
    can_review: false,
    has_purchased: false,
    already_reviewed: false,
    message: ""
  });
  const { userInfo } = useContext(UserContext);
  const { loadProducts, productsLoaded } = useContext(ProductsContext);

  useEffect(() => {
    if (userInfo && product) {
      checkReviewPermission();
    }
  }, [userInfo, product]);

  const checkReviewPermission = async () => {
    try {
      const response = await httpService.get(
        `/api/products/${product.id}/review-permission/`
      );
      setReviewPermission(response.data);
    } catch (error) {
      console.error("Error checking review permission:", error);
    }
  };

  const createReviewHandler = async (e) => {
    e.preventDefault();
    console.log("Creating a review");

    if (!reviewPermission.can_review) {
      setError(reviewPermission.message);
      return;
    }

    try {
      const { data } = await httpService.post(
        `/api/products/${product.id}/reviews/`,
        {
          rating: Number(rating),
          comment,
        }
      );

      setReviews([data, ...reviews]);
      setRating("");
      setComment("");
      setError("");
      // Cập nhật lại permission sau khi review
      checkReviewPermission();
      if (productsLoaded) loadProducts(true);
    } catch (ex) {
      if (ex.response && ex.response.data && ex.response.data.detail)
        setError(ex.response.data.detail);
      else setError(ex.message);
    }
  };

  return (
    <div>
      <h4>Đánh giá sản phẩm</h4>
      {reviews.length === 0 && <Message variant="info">Chưa có đánh giá nào</Message>}
      <ListGroup variant="flush">
        {reviews.map((review) => (
          <ListGroup.Item className="mb-2" key={review.id}>
            <div className="d-flex justify-content-between align-items-start">
              <div className="flex-grow-1">
                <strong>{review.name}</strong>
                <Rating value={review.rating} text={``} color={"#f8e825"} />
                <p className="text-muted small mb-1">
                  {new Date(review.createdAt).toLocaleDateString('vi-VN')}
                </p>
                <p className="mb-0">{review.comment}</p>
              </div>
              {review.can_edit && (
                <div className="ms-2">
                  <Button
                    variant="outline-secondary"
                    size="sm"
                    title="Chỉnh sửa đánh giá"
                  >
                    <i className="fas fa-edit"></i>
                  </Button>
                </div>
              )}
            </div>
          </ListGroup.Item>
        ))}
        <ListGroup.Item>
          <h4>Viết đánh giá</h4>
          {userInfo && userInfo.username ? (
            reviewPermission.can_review ? (
              <Form onSubmit={createReviewHandler}>
                {error && <Message variant="danger">{error}</Message>}
                <Form.Group controlId="rating" className="py-2">
                  <Form.Label>Đánh giá</Form.Label>
                  <Form.Control
                    as="select"
                    value={rating}
                    onChange={(e) => {
                      setRating(e.currentTarget.value);
                    }}
                  >
                    <option value="">Chọn số sao...</option>
                    <option value="1">1 - Rất tệ</option>
                    <option value="2">2 - Tệ</option>
                    <option value="3">3 - Bình thường</option>
                    <option value="4">4 - Tốt</option>
                    <option value="5">5 - Rất tốt</option>
                  </Form.Control>
                </Form.Group>
                <Form.Group controlId="comment" className="py-2">
                  <Form.Label>Nhận xét</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows="5"
                    value={comment}
                    placeholder="Chia sẻ trải nghiệm của bạn về sản phẩm..."
                    onChange={(e) => {
                      setComment(e.currentTarget.value);
                    }}
                  ></Form.Control>
                </Form.Group>
                <Button
                  className="my-2"
                  type="submit"
                  disabled={rating == "" || comment == ""}
                >
                  Gửi đánh giá
                </Button>
              </Form>
            ) : (
              <Message variant={reviewPermission.already_reviewed ? "info" : "warning"}>
                <div>
                  <strong>{reviewPermission.message}</strong>
                  {!reviewPermission.has_purchased && (
                    <div className="mt-2">
                      <small>
                        💡 Bạn cần mua và nhận sản phẩm trước khi có thể đánh giá.
                      </small>
                    </div>
                  )}
                  {reviewPermission.already_reviewed && (
                    <div className="mt-2">
                      <small>
                        ✅ Cảm ơn bạn đã đánh giá sản phẩm này!
                      </small>
                    </div>
                  )}
                </div>
              </Message>
            )
          ) : (
            <Message variant="info">
              Vui lòng <Link to="/login">đăng nhập</Link> để viết đánh giá.
            </Message>
          )}
        </ListGroup.Item>
      </ListGroup>
    </div>
  );
}

export default ReviewsList;
