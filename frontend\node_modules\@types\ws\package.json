{"name": "@types/ws", "version": "8.5.4", "description": "TypeScript definitions for ws", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/loyd", "githubUsername": "loyd"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mlamp", "githubUsername": "mlamp"}, {"name": "<PERSON>", "url": "https://github.com/TitaneBoy", "githubUsername": "TitaneBoy"}, {"name": "reduckted", "url": "https://github.com/reduckted", "githubUsername": "reduckted"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/teidesu", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/wojtkowiak", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/k-yle", "githubUsername": "k-yle"}, {"name": "<PERSON>", "url": "https://github.com/cwadrupldijjit", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/ws"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "d78ccf27d7710f245ab25f9d4957067c8fc0f7aadc035919f7a890427d7360ca", "typeScriptVersion": "4.2", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./package.json": "./package.json"}}