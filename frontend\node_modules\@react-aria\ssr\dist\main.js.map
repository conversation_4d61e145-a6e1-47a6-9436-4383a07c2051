{"mappings": ";;;;;;;;;;;;AAAA;;;;;;;;;;ACAA;;;;;;;;;;CAUC,GAED,0FAA0F;AAC1F,2DAA2D;AAC3D,wDAAwD;AACxD;AAaA,iFAAiF;AACjF,kFAAkF;AAClF,+EAA+E;AAC/E,+EAA+E;AAC/E,2DAA2D;AAC3D,MAAM,uCAAkC;IACtC,QAAQ,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;IAC1C,SAAS;AACX;AAEA,MAAM,iDAAa,CAAA,GAAA,sCAAK,AAAD,EAAE,aAAa,CAAkB;AAWjD,SAAS,0CAAY,KAAuB,EAAe;IAChE,IAAI,MAAM,CAAA,GAAA,uBAAS,EAAE;IACrB,IAAI,QAAyB,CAAA,GAAA,oBAAO,AAAD,EAAE,IAAO,CAAA;YAC1C,iFAAiF;YACjF,oCAAoC;YACpC,QAAQ,QAAQ,uCAAiB,KAAK,CAAC,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,EAAE,IAAI,OAAO,CAAC,CAAC;YACtE,SAAS;QACX,CAAA,GAAI;QAAC;KAAI;IAET,qBACE,0DAAC,iCAAW,QAAQ;QAAC,OAAO;OACzB,MAAM,QAAQ;AAGrB;AAEA,IAAI,kCAAY,QACd,OAAO,WAAW,eAClB,OAAO,QAAQ,IACf,OAAO,QAAQ,CAAC,aAAa;AAIxB,SAAS,0CAAa,SAAkB,EAAU;IACvD,IAAI,MAAM,CAAA,GAAA,uBAAS,EAAE;IAErB,4EAA4E;IAC5E,yDAAyD;IACzD,IAAI,QAAQ,wCAAkB,CAAC,iCAC7B,QAAQ,IAAI,CAAC;IAGf,uDAAuD;IACvD,OAAO,CAAA,GAAA,oBAAM,EAAE,IAAM,aAAa,CAAC,UAAU,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,EAAE,IAAI,OAAO,CAAC,CAAC,EAAE;QAAC;KAAU;AAC3F;AAOO,SAAS,4CAAoB;IAClC,IAAI,MAAM,CAAA,GAAA,uBAAS,EAAE;IACrB,IAAI,iBAAiB,QAAQ;IAC7B,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qBAAQ,AAAD,EAAE;IAEjC,qEAAqE;IACrE,yEAAyE;IACzE,IAAI,OAAO,WAAW,eAAe,gBACnC,uEAAuE;IACvE,sDAAsD;IACtD,sDAAsD;IACtD,CAAA,GAAA,4BAAe,AAAD,EAAE,IAAM;QACpB,SAAS,KAAK;IAChB,GAAG,EAAE;IAGP,OAAO;AACT;;CDjGC,GACD", "sources": ["packages/@react-aria/ssr/src/index.ts", "packages/@react-aria/ssr/src/SSRProvider.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nexport {SSRProvider, useSSRSafeId, useIsSSR} from './SSRProvider';\nexport type {SSRProviderProps} from './SSRProvider';\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// We must avoid a circular dependency with @react-aria/utils, and this useLayoutEffect is\n// guarded by a check that it only runs on the client side.\n// eslint-disable-next-line rulesdir/useLayoutEffectRule\nimport React, {ReactNode, useContext, useLayoutEffect, useMemo, useState} from 'react';\n\n// To support SSR, the auto incrementing id counter is stored in a context. This allows\n// it to be reset on every request to ensure the client and server are consistent.\n// There is also a prefix string that is used to support async loading components\n// Each async boundary must be wrapped in an SSR provider, which appends to the prefix\n// and resets the current id counter. This ensures that async loaded components have\n// consistent ids regardless of the loading order.\ninterface SSRContextValue {\n  prefix: string,\n  current: number\n}\n\n// Default context value to use in case there is no SSRProvider. This is fine for\n// client-only apps. In order to support multiple copies of React Aria potentially\n// being on the page at once, the prefix is set to a random number. SSRProvider\n// will reset this to zero for consistency between server and client, so in the\n// SSR case multiple copies of React Aria is not supported.\nconst defaultContext: SSRContextValue = {\n  prefix: String(Math.round(Math.random() * 10000000000)),\n  current: 0\n};\n\nconst SSRContext = React.createContext<SSRContextValue>(defaultContext);\n\nexport interface SSRProviderProps {\n  /** Your application here. */\n  children: ReactNode\n}\n\n/**\n * When using SSR with React Aria, applications must be wrapped in an SSRProvider.\n * This ensures that auto generated ids are consistent between the client and server.\n */\nexport function SSRProvider(props: SSRProviderProps): JSX.Element {\n  let cur = useContext(SSRContext);\n  let value: SSRContextValue = useMemo(() => ({\n    // If this is the first SSRProvider, start with an empty string prefix, otherwise\n    // append and increment the counter.\n    prefix: cur === defaultContext ? '' : `${cur.prefix}-${++cur.current}`,\n    current: 0\n  }), [cur]);\n\n  return (\n    <SSRContext.Provider value={value}>\n      {props.children}\n    </SSRContext.Provider>\n  );\n}\n\nlet canUseDOM = Boolean(\n  typeof window !== 'undefined' &&\n  window.document &&\n  window.document.createElement\n);\n\n/** @private */\nexport function useSSRSafeId(defaultId?: string): string {\n  let ctx = useContext(SSRContext);\n\n  // If we are rendering in a non-DOM environment, and there's no SSRProvider,\n  // provide a warning to hint to the developer to add one.\n  if (ctx === defaultContext && !canUseDOM) {\n    console.warn('When server rendering, you must wrap your application in an <SSRProvider> to ensure consistent ids are generated between the client and server.');\n  }\n\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return useMemo(() => defaultId || `react-aria${ctx.prefix}-${++ctx.current}`, [defaultId]);\n}\n\n/**\n * Returns whether the component is currently being server side rendered or\n * hydrated on the client. Can be used to delay browser-specific rendering\n * until after hydration.\n */\nexport function useIsSSR(): boolean {\n  let cur = useContext(SSRContext);\n  let isInSSRContext = cur !== defaultContext;\n  let [isSSR, setIsSSR] = useState(isInSSRContext);\n\n  // If on the client, and the component was initially server rendered,\n  // then schedule a layout effect to update the component after hydration.\n  if (typeof window !== 'undefined' && isInSSRContext) {\n    // This if statement technically breaks the rules of hooks, but is safe\n    // because the condition never changes after mounting.\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useLayoutEffect(() => {\n      setIsSSR(false);\n    }, []);\n  }\n\n  return isSSR;\n}\n"], "names": [], "version": 3, "file": "main.js.map"}