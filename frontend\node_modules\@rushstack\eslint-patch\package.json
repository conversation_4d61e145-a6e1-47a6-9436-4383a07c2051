{"name": "@rushstack/eslint-patch", "version": "1.2.0", "description": "A patch that improves how ESLint loads plugins when working in a monorepo with a reusable toolchain", "main": "lib/usage.js", "license": "MIT", "repository": {"url": "https://github.com/microsoft/rushstack.git", "type": "git", "directory": "eslint/eslint-patch"}, "homepage": "https://rushstack.io", "keywords": ["eslintrc", "config", "module", "resolve", "resolver", "plugin", "relative", "package"], "devDependencies": {"@rushstack/heft": "0.47.0", "@rushstack/heft-node-rig": "1.10.0", "@types/node": "12.20.24"}, "scripts": {"build": "heft build --clean", "_phase:build": "heft build --clean"}}