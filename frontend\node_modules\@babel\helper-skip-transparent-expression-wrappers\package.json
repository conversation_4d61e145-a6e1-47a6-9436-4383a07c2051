{"name": "@babel/helper-skip-transparent-expression-wrappers", "version": "7.20.0", "description": "Helper which skips types and parentheses", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "dependencies": {"@babel/types": "^7.20.0"}, "devDependencies": {"@babel/traverse": "^7.20.0"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}