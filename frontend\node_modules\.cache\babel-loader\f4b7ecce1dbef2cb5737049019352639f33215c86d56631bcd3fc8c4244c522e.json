{"ast": null, "code": "var _jsxFileName = \"D:\\\\CHUONGTRINHHOC\\\\DATN\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\header.jsx\",\n  _s = $RefreshSig$();\nimport React, { useContext } from \"react\";\nimport { Container, Nav, Navbar, NavDropdown } from \"react-bootstrap\";\nimport { LinkContainer } from \"react-router-bootstrap\";\nimport UserContext from \"../context/userContext\";\nimport SearchBox from \"./searchBox\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Header(_ref) {\n  _s();\n  let {\n    keyword,\n    setKeyword\n  } = _ref;\n  const {\n    userInfo\n  } = useContext(UserContext);\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    children: /*#__PURE__*/_jsxDEV(Navbar, {\n      bg: \"dark\",\n      variant: \"dark\",\n      expand: \"lg\",\n      collapseOnSelect: true,\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        className: \"\",\n        children: [/*#__PURE__*/_jsxDEV(LinkContainer, {\n          to: \"/\",\n          children: /*#__PURE__*/_jsxDEV(Navbar.Brand, {\n            children: \"Proshop\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SearchBox, {\n          keyword: keyword,\n          setKeyword: setKeyword\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Navbar.Toggle, {\n          \"aria-controls\": \"basic-navbar-nav\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Navbar.Collapse, {\n          id: \"basic-navbar-nav\",\n          children: /*#__PURE__*/_jsxDEV(Nav, {\n            className: \"ms-auto\",\n            children: [/*#__PURE__*/_jsxDEV(LinkContainer, {\n              to: \"/cart\",\n              children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-shopping-cart\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 23,\n                  columnNumber: 19\n                }, this), \" Cart\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 22,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 15\n            }, this), userInfo && /*#__PURE__*/_jsxDEV(LinkContainer, {\n              to: \"/wishlist\",\n              children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-heart\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 29,\n                  columnNumber: 21\n                }, this), \" Wishlist\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 17\n            }, this), userInfo && /*#__PURE__*/_jsxDEV(NavDropdown, {\n              title: userInfo.username,\n              id: \"username\",\n              children: [/*#__PURE__*/_jsxDEV(LinkContainer, {\n                to: \"/profile\",\n                children: /*#__PURE__*/_jsxDEV(NavDropdown.Item, {\n                  children: \"Profile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 36,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 35,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(LinkContainer, {\n                to: \"/logout\",\n                children: /*#__PURE__*/_jsxDEV(NavDropdown.Item, {\n                  children: \"Logout\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 39,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 17\n            }, this), !userInfo && /*#__PURE__*/_jsxDEV(LinkContainer, {\n              to: \"/login\",\n              children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-user\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 46,\n                  columnNumber: 21\n                }, this), \" Login\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n}\n_s(Header, \"/KD1s1jRBq3X+az2/mCvD2j7GYM=\");\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useContext", "Container", "Nav", "<PERSON><PERSON><PERSON>", "NavDropdown", "LinkContainer", "UserContext", "SearchBox", "jsxDEV", "_jsxDEV", "Header", "_ref", "_s", "keyword", "setKeyword", "userInfo", "children", "bg", "variant", "expand", "collapseOnSelect", "className", "to", "Brand", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Toggle", "Collapse", "id", "Link", "title", "username", "<PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["D:/CHUONGTRINHHOC/DATN/Python-KienTap-/frontend/src/components/header.jsx"], "sourcesContent": ["import React, { useContext } from \"react\";\r\nimport { Container, Nav, Navbar, NavDropdown } from \"react-bootstrap\";\r\nimport { LinkContainer } from \"react-router-bootstrap\";\r\nimport UserContext from \"../context/userContext\";\r\nimport SearchBox from \"./searchBox\";\r\n\r\nfunction Header({keyword,setKeyword}) {\r\n  const { userInfo } = useContext(UserContext);\r\n\r\n  return (\r\n    <header>\r\n      <Navbar bg=\"dark\" variant=\"dark\" expand=\"lg\" collapseOnSelect>\r\n        <Container className=\"\">\r\n          <LinkContainer to=\"/\">\r\n            <Navbar.Brand>Proshop</Navbar.Brand>\r\n          </LinkContainer>\r\n          <SearchBox keyword={keyword} setKeyword={setKeyword}/>\r\n          <Navbar.Toggle aria-controls=\"basic-navbar-nav\" />\r\n          <Navbar.Collapse id=\"basic-navbar-nav\">\r\n            <Nav className=\"ms-auto\">\r\n              <LinkContainer to=\"/cart\">\r\n                <Nav.Link>\r\n                  <i className=\"fas fa-shopping-cart\" /> Cart\r\n                </Nav.Link>\r\n              </LinkContainer>\r\n              {userInfo && (\r\n                <LinkContainer to=\"/wishlist\">\r\n                  <Nav.Link>\r\n                    <i className=\"fas fa-heart\" /> Wishlist\r\n                  </Nav.Link>\r\n                </LinkContainer>\r\n              )}\r\n              {userInfo && (\r\n                <NavDropdown title={userInfo.username} id=\"username\">\r\n                  <LinkContainer to=\"/profile\">\r\n                    <NavDropdown.Item>Profile</NavDropdown.Item>\r\n                  </LinkContainer>\r\n                  <LinkContainer to=\"/logout\">\r\n                    <NavDropdown.Item>Logout</NavDropdown.Item>\r\n                  </LinkContainer>\r\n                </NavDropdown>\r\n              )}\r\n              {!userInfo && (\r\n                <LinkContainer to=\"/login\">\r\n                  <Nav.Link>\r\n                    <i className=\"fas fa-user\" /> Login\r\n                  </Nav.Link>\r\n                </LinkContainer>\r\n              )}\r\n            </Nav>\r\n          </Navbar.Collapse>\r\n        </Container>\r\n      </Navbar>\r\n    </header>\r\n  );\r\n}\r\n\r\nexport default Header;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,SAASC,SAAS,EAAEC,GAAG,EAAEC,MAAM,EAAEC,WAAW,QAAQ,iBAAiB;AACrE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,SAAS,MAAM,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,SAASC,MAAMA,CAAAC,IAAA,EAAuB;EAAAC,EAAA;EAAA,IAAtB;IAACC,OAAO;IAACC;EAAU,CAAC,GAAAH,IAAA;EAClC,MAAM;IAAEI;EAAS,CAAC,GAAGf,UAAU,CAACM,WAAW,CAAC;EAE5C,oBACEG,OAAA;IAAAO,QAAA,eACEP,OAAA,CAACN,MAAM;MAACc,EAAE,EAAC,MAAM;MAACC,OAAO,EAAC,MAAM;MAACC,MAAM,EAAC,IAAI;MAACC,gBAAgB;MAAAJ,QAAA,eAC3DP,OAAA,CAACR,SAAS;QAACoB,SAAS,EAAC,EAAE;QAAAL,QAAA,gBACrBP,OAAA,CAACJ,aAAa;UAACiB,EAAE,EAAC,GAAG;UAAAN,QAAA,eACnBP,OAAA,CAACN,MAAM,CAACoB,KAAK;YAAAP,QAAA,EAAC;UAAO;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAe;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACtB,eAChBlB,OAAA,CAACF,SAAS;UAACM,OAAO,EAAEA,OAAQ;UAACC,UAAU,EAAEA;QAAW;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAE,eACtDlB,OAAA,CAACN,MAAM,CAACyB,MAAM;UAAC,iBAAc;QAAkB;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAG,eAClDlB,OAAA,CAACN,MAAM,CAAC0B,QAAQ;UAACC,EAAE,EAAC,kBAAkB;UAAAd,QAAA,eACpCP,OAAA,CAACP,GAAG;YAACmB,SAAS,EAAC,SAAS;YAAAL,QAAA,gBACtBP,OAAA,CAACJ,aAAa;cAACiB,EAAE,EAAC,OAAO;cAAAN,QAAA,eACvBP,OAAA,CAACP,GAAG,CAAC6B,IAAI;gBAAAf,QAAA,gBACPP,OAAA;kBAAGY,SAAS,EAAC;gBAAsB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAG,SACxC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAW;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACG,EACfZ,QAAQ,iBACPN,OAAA,CAACJ,aAAa;cAACiB,EAAE,EAAC,WAAW;cAAAN,QAAA,eAC3BP,OAAA,CAACP,GAAG,CAAC6B,IAAI;gBAAAf,QAAA,gBACPP,OAAA;kBAAGY,SAAS,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAG,aAChC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAW;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAEd,EACAZ,QAAQ,iBACPN,OAAA,CAACL,WAAW;cAAC4B,KAAK,EAAEjB,QAAQ,CAACkB,QAAS;cAACH,EAAE,EAAC,UAAU;cAAAd,QAAA,gBAClDP,OAAA,CAACJ,aAAa;gBAACiB,EAAE,EAAC,UAAU;gBAAAN,QAAA,eAC1BP,OAAA,CAACL,WAAW,CAAC8B,IAAI;kBAAAlB,QAAA,EAAC;gBAAO;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAAmB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAC9B,eAChBlB,OAAA,CAACJ,aAAa;gBAACiB,EAAE,EAAC,SAAS;gBAAAN,QAAA,eACzBP,OAAA,CAACL,WAAW,CAAC8B,IAAI;kBAAAlB,QAAA,EAAC;gBAAM;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAAmB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAC7B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAEnB,EACA,CAACZ,QAAQ,iBACRN,OAAA,CAACJ,aAAa;cAACiB,EAAE,EAAC,QAAQ;cAAAN,QAAA,eACxBP,OAAA,CAACP,GAAG,CAAC6B,IAAI;gBAAAf,QAAA,gBACPP,OAAA;kBAAGY,SAAS,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAG,UAC/B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAW;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAEd;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACG;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACU;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACR;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EACL;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACF;AAEb;AAACf,EAAA,CAjDQF,MAAM;AAAAyB,EAAA,GAANzB,MAAM;AAmDf,eAAeA,MAAM;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}