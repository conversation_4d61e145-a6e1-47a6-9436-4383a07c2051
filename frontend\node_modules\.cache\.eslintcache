[{"D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\index.js": "1", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\App.js": "2", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\reportWebVitals.js": "3", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\context\\productsContext.js": "4", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\context\\userContext.js": "5", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\context\\cartContext.js": "6", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\header.jsx": "7", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx": "8", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\footer.jsx": "9", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx": "10", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\ProtectedRoute.jsx": "11", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx": "12", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx": "13", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx": "14", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx": "15", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx": "16", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx": "17", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx": "18", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx": "19", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx": "20", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx": "21", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx": "22", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminDashboard.jsx": "23", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminUsers.jsx": "24", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminBrands.jsx": "25", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCategories.jsx": "26", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminOrders.jsx": "27", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminReviews.jsx": "28", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminProducts.jsx": "29", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\loader.jsx": "30", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\utils\\currency.js": "31", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\services\\httpService.js": "32", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\rating.jsx": "33", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx": "34", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx": "35", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\product.jsx": "36", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\brandCard.jsx": "37", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\categoryCard.jsx": "38", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\productsCarousel.jsx": "39", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\AdminRedirect.jsx": "40", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx": "41", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx": "42", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\message.jsx": "43", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx": "44", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx": "45", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminLayout.jsx": "46", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx": "47", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminHeader.jsx": "48", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminSidebar.jsx": "49", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\wishlistButton.jsx": "50", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\wishlistPage.jsx": "51"}, {"size": 670, "mtime": 1751117666040, "results": "52", "hashOfConfig": "53"}, {"size": 5343, "mtime": 1751163896491, "results": "54", "hashOfConfig": "53"}, {"size": 375, "mtime": 1751117666060, "results": "55", "hashOfConfig": "53"}, {"size": 1648, "mtime": 1751117666039, "results": "56", "hashOfConfig": "53"}, {"size": 4611, "mtime": 1751117666040, "results": "57", "hashOfConfig": "53"}, {"size": 4424, "mtime": 1751117666039, "results": "58", "hashOfConfig": "53"}, {"size": 2098, "mtime": 1751163870902, "results": "59", "hashOfConfig": "53"}, {"size": 5140, "mtime": 1751163853733, "results": "60", "hashOfConfig": "53"}, {"size": 584, "mtime": 1751117666034, "results": "61", "hashOfConfig": "53"}, {"size": 2056, "mtime": 1751117666056, "results": "62", "hashOfConfig": "53"}, {"size": 572, "mtime": 1751117666030, "results": "63", "hashOfConfig": "53"}, {"size": 3186, "mtime": 1751117666056, "results": "64", "hashOfConfig": "53"}, {"size": 2832, "mtime": 1751117666059, "results": "65", "hashOfConfig": "53"}, {"size": 4196, "mtime": 1751117666045, "results": "66", "hashOfConfig": "53"}, {"size": 3480, "mtime": 1751117666059, "results": "67", "hashOfConfig": "53"}, {"size": 577, "mtime": 1751117666057, "results": "68", "hashOfConfig": "53"}, {"size": 3266, "mtime": 1751117666060, "results": "69", "hashOfConfig": "53"}, {"size": 5289, "mtime": 1751117666058, "results": "70", "hashOfConfig": "53"}, {"size": 6349, "mtime": 1751117666057, "results": "71", "hashOfConfig": "53"}, {"size": 1940, "mtime": 1751117666055, "results": "72", "hashOfConfig": "53"}, {"size": 1935, "mtime": 1751117666058, "results": "73", "hashOfConfig": "53"}, {"size": 4755, "mtime": 1751117666059, "results": "74", "hashOfConfig": "53"}, {"size": 7840, "mtime": 1751117666043, "results": "75", "hashOfConfig": "53"}, {"size": 13874, "mtime": 1751117666045, "results": "76", "hashOfConfig": "53"}, {"size": 8343, "mtime": 1751117666042, "results": "77", "hashOfConfig": "53"}, {"size": 8526, "mtime": 1751117666042, "results": "78", "hashOfConfig": "53"}, {"size": 10873, "mtime": 1751117666044, "results": "79", "hashOfConfig": "53"}, {"size": 8700, "mtime": 1751117666045, "results": "80", "hashOfConfig": "53"}, {"size": 14511, "mtime": 1751117666045, "results": "81", "hashOfConfig": "53"}, {"size": 413, "mtime": 1751117666035, "results": "82", "hashOfConfig": "53"}, {"size": 1359, "mtime": 1751117666061, "results": "83", "hashOfConfig": "53"}, {"size": 1624, "mtime": 1751117666061, "results": "84", "hashOfConfig": "53"}, {"size": 663, "mtime": 1751117666038, "results": "85", "hashOfConfig": "53"}, {"size": 1262, "mtime": 1751117666038, "results": "86", "hashOfConfig": "53"}, {"size": 3771, "mtime": 1751117666038, "results": "87", "hashOfConfig": "53"}, {"size": 1492, "mtime": 1751163827203, "results": "88", "hashOfConfig": "53"}, {"size": 760, "mtime": 1751117666033, "results": "89", "hashOfConfig": "53"}, {"size": 1024, "mtime": 1751117666033, "results": "90", "hashOfConfig": "53"}, {"size": 979, "mtime": 1751117666037, "results": "91", "hashOfConfig": "53"}, {"size": 727, "mtime": 1751117666029, "results": "92", "hashOfConfig": "53"}, {"size": 2595, "mtime": 1751117666036, "results": "93", "hashOfConfig": "53"}, {"size": 387, "mtime": 1751117666035, "results": "94", "hashOfConfig": "53"}, {"size": 239, "mtime": 1751117666036, "results": "95", "hashOfConfig": "53"}, {"size": 1574, "mtime": 1751117666034, "results": "96", "hashOfConfig": "53"}, {"size": 2631, "mtime": 1751161175854, "results": "97", "hashOfConfig": "53"}, {"size": 465, "mtime": 1751117666032, "results": "98", "hashOfConfig": "53"}, {"size": 1895, "mtime": 1751117666036, "results": "99", "hashOfConfig": "53"}, {"size": 3227, "mtime": 1751117666030, "results": "100", "hashOfConfig": "53"}, {"size": 3420, "mtime": 1751117666032, "results": "101", "hashOfConfig": "53"}, {"size": 2243, "mtime": 1751163778508, "results": "102", "hashOfConfig": "53"}, {"size": 5552, "mtime": 1751163975226, "results": "103", "hashOfConfig": "53"}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1sqkrsn", {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "212"}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\index.js", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\App.js", ["258"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\reportWebVitals.js", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\context\\productsContext.js", ["259"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\context\\userContext.js", ["260", "261", "262", "263", "264", "265", "266", "267", "268"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\context\\cartContext.js", ["269", "270", "271"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\header.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx", ["272", "273"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\footer.jsx", ["274"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx", ["275", "276"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\ProtectedRoute.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx", ["277", "278"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx", ["279"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx", ["280", "281"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx", ["282", "283"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx", ["284"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx", ["285"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx", ["286", "287", "288"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx", ["289", "290", "291", "292"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx", ["293"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx", ["294", "295", "296", "297", "298", "299", "300", "301", "302"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminDashboard.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminUsers.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminBrands.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCategories.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminOrders.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminReviews.jsx", ["303"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminProducts.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\loader.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\utils\\currency.js", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\services\\httpService.js", ["304", "305"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\rating.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx", ["306"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx", ["307", "308"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\product.jsx", [], [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\brandCard.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\categoryCard.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\productsCarousel.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\AdminRedirect.jsx", ["309"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx", ["310"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\message.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminLayout.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx", ["311", "312", "313"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminHeader.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminSidebar.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\wishlistButton.jsx", ["314"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\wishlistPage.jsx", [], [], {"ruleId": "315", "severity": 1, "message": "316", "line": 42, "column": 3, "nodeType": "317", "endLine": 42, "endColumn": 12, "suggestions": "318"}, {"ruleId": "319", "severity": 1, "message": "320", "line": 35, "column": 49, "nodeType": "321", "messageId": "322", "endLine": 35, "endColumn": 51}, {"ruleId": "323", "severity": 1, "message": "324", "line": 58, "column": 15, "nodeType": "317", "messageId": "325", "endLine": 58, "endColumn": 19}, {"ruleId": "315", "severity": 1, "message": "326", "line": 104, "column": 6, "nodeType": "327", "endLine": 104, "endColumn": 8, "suggestions": "328"}, {"ruleId": "315", "severity": 1, "message": "329", "line": 116, "column": 6, "nodeType": "327", "endLine": 116, "endColumn": 18, "suggestions": "330"}, {"ruleId": "319", "severity": 1, "message": "331", "line": 121, "column": 20, "nodeType": "321", "messageId": "322", "endLine": 121, "endColumn": 22}, {"ruleId": "319", "severity": 1, "message": "331", "line": 121, "column": 53, "nodeType": "321", "messageId": "322", "endLine": 121, "endColumn": 55}, {"ruleId": "319", "severity": 1, "message": "331", "line": 125, "column": 17, "nodeType": "321", "messageId": "322", "endLine": 125, "endColumn": 19}, {"ruleId": "319", "severity": 1, "message": "331", "line": 125, "column": 44, "nodeType": "321", "messageId": "322", "endLine": 125, "endColumn": 46}, {"ruleId": "319", "severity": 1, "message": "331", "line": 129, "column": 20, "nodeType": "321", "messageId": "322", "endLine": 129, "endColumn": 22}, {"ruleId": "319", "severity": 1, "message": "320", "line": 133, "column": 16, "nodeType": "321", "messageId": "322", "endLine": 133, "endColumn": 18}, {"ruleId": "319", "severity": 1, "message": "320", "line": 63, "column": 18, "nodeType": "321", "messageId": "322", "endLine": 63, "endColumn": 20}, {"ruleId": "319", "severity": 1, "message": "320", "line": 69, "column": 15, "nodeType": "321", "messageId": "322", "endLine": 69, "endColumn": 17}, {"ruleId": "319", "severity": 1, "message": "320", "line": 127, "column": 45, "nodeType": "321", "messageId": "322", "endLine": 127, "endColumn": 47}, {"ruleId": "315", "severity": 1, "message": "332", "line": 36, "column": 6, "nodeType": "327", "endLine": 36, "endColumn": 8, "suggestions": "333"}, {"ruleId": "319", "severity": 1, "message": "331", "line": 45, "column": 13, "nodeType": "321", "messageId": "322", "endLine": 45, "endColumn": 15}, {"ruleId": "334", "severity": 1, "message": "335", "line": 14, "column": 13, "nodeType": "336", "messageId": "337", "endLine": 14, "endColumn": 107, "fix": "338"}, {"ruleId": "315", "severity": 1, "message": "339", "line": 22, "column": 6, "nodeType": "327", "endLine": 22, "endColumn": 8, "suggestions": "340"}, {"ruleId": "319", "severity": 1, "message": "331", "line": 26, "column": 13, "nodeType": "321", "messageId": "322", "endLine": 26, "endColumn": 15}, {"ruleId": "323", "severity": 1, "message": "341", "line": 13, "column": 23, "nodeType": "317", "messageId": "325", "endLine": 13, "endColumn": 38}, {"ruleId": "315", "severity": 1, "message": "342", "line": 27, "column": 6, "nodeType": "327", "endLine": 27, "endColumn": 8, "suggestions": "343"}, {"ruleId": "315", "severity": 1, "message": "344", "line": 22, "column": 6, "nodeType": "327", "endLine": 22, "endColumn": 8, "suggestions": "345"}, {"ruleId": "323", "severity": 1, "message": "346", "line": 1, "column": 29, "nodeType": "317", "messageId": "325", "endLine": 1, "endColumn": 38}, {"ruleId": "319", "severity": 1, "message": "331", "line": 26, "column": 13, "nodeType": "321", "messageId": "322", "endLine": 26, "endColumn": 15}, {"ruleId": "347", "severity": 1, "message": "348", "line": 18, "column": 28, "nodeType": "317", "messageId": "349", "endLine": 18, "endColumn": 36}, {"ruleId": "315", "severity": 1, "message": "342", "line": 22, "column": 6, "nodeType": "327", "endLine": 22, "endColumn": 8, "suggestions": "350"}, {"ruleId": "315", "severity": 1, "message": "351", "line": 15, "column": 6, "nodeType": "327", "endLine": 15, "endColumn": 8, "suggestions": "352"}, {"ruleId": "315", "severity": 1, "message": "344", "line": 31, "column": 6, "nodeType": "327", "endLine": 31, "endColumn": 8, "suggestions": "353"}, {"ruleId": "323", "severity": 1, "message": "354", "line": 30, "column": 11, "nodeType": "317", "messageId": "325", "endLine": 30, "endColumn": 13}, {"ruleId": "319", "severity": 1, "message": "320", "line": 60, "column": 38, "nodeType": "321", "messageId": "322", "endLine": 60, "endColumn": 40}, {"ruleId": "319", "severity": 1, "message": "320", "line": 129, "column": 53, "nodeType": "321", "messageId": "322", "endLine": 129, "endColumn": 55}, {"ruleId": "319", "severity": 1, "message": "320", "line": 27, "column": 47, "nodeType": "321", "messageId": "322", "endLine": 27, "endColumn": 49}, {"ruleId": "319", "severity": 1, "message": "320", "line": 28, "column": 47, "nodeType": "321", "messageId": "322", "endLine": 28, "endColumn": 49}, {"ruleId": "315", "severity": 1, "message": "355", "line": 35, "column": 6, "nodeType": "327", "endLine": 35, "endColumn": 8, "suggestions": "356"}, {"ruleId": "319", "severity": 1, "message": "331", "line": 41, "column": 17, "nodeType": "321", "messageId": "322", "endLine": 41, "endColumn": 19}, {"ruleId": "319", "severity": 1, "message": "320", "line": 37, "column": 33, "nodeType": "321", "messageId": "322", "endLine": 37, "endColumn": 35}, {"ruleId": "323", "severity": 1, "message": "341", "line": 18, "column": 24, "nodeType": "317", "messageId": "325", "endLine": 18, "endColumn": 39}, {"ruleId": "315", "severity": 1, "message": "357", "line": 43, "column": 6, "nodeType": "327", "endLine": 43, "endColumn": 8, "suggestions": "358"}, {"ruleId": "319", "severity": 1, "message": "331", "line": 49, "column": 21, "nodeType": "321", "messageId": "322", "endLine": 49, "endColumn": 23}, {"ruleId": "319", "severity": 1, "message": "320", "line": 51, "column": 34, "nodeType": "321", "messageId": "322", "endLine": 51, "endColumn": 36}, {"ruleId": "319", "severity": 1, "message": "331", "line": 55, "column": 24, "nodeType": "321", "messageId": "322", "endLine": 55, "endColumn": 26}, {"ruleId": "319", "severity": 1, "message": "320", "line": 57, "column": 37, "nodeType": "321", "messageId": "322", "endLine": 57, "endColumn": 39}, {"ruleId": "319", "severity": 1, "message": "331", "line": 63, "column": 13, "nodeType": "321", "messageId": "322", "endLine": 63, "endColumn": 15}, {"ruleId": "319", "severity": 1, "message": "331", "line": 63, "column": 35, "nodeType": "321", "messageId": "322", "endLine": 63, "endColumn": 37}, {"ruleId": "319", "severity": 1, "message": "331", "line": 66, "column": 20, "nodeType": "321", "messageId": "322", "endLine": 66, "endColumn": 22}, {"ruleId": "323", "severity": 1, "message": "359", "line": 9, "column": 10, "nodeType": "317", "messageId": "325", "endLine": 9, "endColumn": 18}, {"ruleId": "319", "severity": 1, "message": "320", "line": 13, "column": 13, "nodeType": "321", "messageId": "322", "endLine": 13, "endColumn": 15}, {"ruleId": "360", "severity": 1, "message": "361", "line": 46, "column": 1, "nodeType": "362", "endLine": 53, "endColumn": 3}, {"ruleId": "323", "severity": 1, "message": "346", "line": 1, "column": 17, "nodeType": "317", "messageId": "325", "endLine": 1, "endColumn": 26}, {"ruleId": "319", "severity": 1, "message": "320", "line": 90, "column": 34, "nodeType": "321", "messageId": "322", "endLine": 90, "endColumn": 36}, {"ruleId": "319", "severity": 1, "message": "320", "line": 90, "column": 51, "nodeType": "321", "messageId": "322", "endLine": 90, "endColumn": 53}, {"ruleId": "323", "severity": 1, "message": "363", "line": 1, "column": 8, "nodeType": "317", "messageId": "325", "endLine": 1, "endColumn": 13}, {"ruleId": "319", "severity": 1, "message": "320", "line": 22, "column": 47, "nodeType": "321", "messageId": "322", "endLine": 22, "endColumn": 49}, {"ruleId": "323", "severity": 1, "message": "346", "line": 1, "column": 17, "nodeType": "317", "messageId": "325", "endLine": 1, "endColumn": 26}, {"ruleId": "323", "severity": 1, "message": "364", "line": 8, "column": 8, "nodeType": "317", "messageId": "325", "endLine": 8, "endColumn": 19}, {"ruleId": "323", "severity": 1, "message": "365", "line": 14, "column": 10, "nodeType": "317", "messageId": "325", "endLine": 14, "endColumn": 15}, {"ruleId": "315", "severity": 1, "message": "366", "line": 15, "column": 6, "nodeType": "327", "endLine": 15, "endColumn": 27, "suggestions": "367"}, "react-hooks/exhaustive-deps", "React Hook useEffect contains a call to 'setKeyword'. Without a list of dependencies, this can lead to an infinite chain of updates. To fix this, pass [keywordParam] as a second argument to the useEffect Hook.", "Identifier", ["368"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "no-unused-vars", "'data' is assigned a value but never used.", "unusedVar", "React Hook useEffect has missing dependencies: 'authTokens' and 'refresh'. Either include them or remove the dependency array.", "ArrayExpression", ["369"], "React Hook useEffect has a missing dependency: 'refresh'. Either include it or remove the dependency array.", ["370"], "Expected '!==' and instead saw '!='.", "React Hook useEffect has missing dependencies: 'id' and 'loadProduct'. Either include them or remove the dependency array.", ["371"], "react/jsx-no-target-blank", "Using target=\"_blank\" without rel=\"noreferrer\" (which implies rel=\"noopener\") is a security risk in older browsers: see https://mathiasbynens.github.io/rel-noopener/#recommendations", "JSXOpeningElement", "noTargetBlankWithoutNoreferrer", {"range": "372", "text": "373"}, "React Hook useEffect has a missing dependency: 'loadProducts'. Either include it or remove the dependency array.", ["374"], "'setSearchParams' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'navigate', 'redirect', and 'userInfo'. Either include them or remove the dependency array.", ["375"], "React Hook useEffect has missing dependencies: 'navigate' and 'userInfo'. Either include them or remove the dependency array.", ["376"], "'useEffect' is defined but never used.", "no-const-assign", "'redirect' is constant.", "const", ["377"], "React Hook useEffect has missing dependencies: 'logout', 'navigate', and 'userInfo'. Either include them or remove the dependency array.", ["378"], ["379"], "'id' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'id' and 'logout'. Either include them or remove the dependency array.", ["380"], "React Hook useEffect has missing dependencies: 'brandParam', 'categoryParam', and 'loadProducts'. Either include them or remove the dependency array.", ["381"], "'products' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'React' is defined but never used.", "'httpService' is defined but never used.", "'email' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'checkWishlistStatus'. Either include it or remove the dependency array.", ["382"], {"desc": "383", "fix": "384"}, {"desc": "385", "fix": "386"}, {"desc": "385", "fix": "387"}, {"desc": "388", "fix": "389"}, [460, 460], " rel=\"noreferrer\"", {"desc": "390", "fix": "391"}, {"desc": "392", "fix": "393"}, {"desc": "394", "fix": "395"}, {"desc": "392", "fix": "396"}, {"desc": "397", "fix": "398"}, {"desc": "394", "fix": "399"}, {"desc": "400", "fix": "401"}, {"desc": "402", "fix": "403"}, {"desc": "404", "fix": "405"}, "Add dependencies array: [keywordParam]", {"range": "406", "text": "407"}, "Update the dependencies array to be: [authTokens, refresh]", {"range": "408", "text": "409"}, {"range": "410", "text": "409"}, "Update the dependencies array to be: [id, loadProduct]", {"range": "411", "text": "412"}, "Update the dependencies array to be: [loadProducts]", {"range": "413", "text": "414"}, "Update the dependencies array to be: [navigate, redirect, userInfo]", {"range": "415", "text": "416"}, "Update the dependencies array to be: [navigate, userInfo]", {"range": "417", "text": "418"}, {"range": "419", "text": "416"}, "Update the dependencies array to be: [logout, navigate, userInfo]", {"range": "420", "text": "421"}, {"range": "422", "text": "418"}, "Update the dependencies array to be: [id, logout]", {"range": "423", "text": "424"}, "Update the dependencies array to be: [brandParam, categoryParam, loadProducts]", {"range": "425", "text": "426"}, "Update the dependencies array to be: [userInfo, productId, checkWishlistStatus]", {"range": "427", "text": "428"}, [1926, 1926], ", [keywordParam]", [2899, 2901], "[authTokens, refresh]", [3287, 3299], [1152, 1154], "[id, loadProduct]", [860, 862], "[loadProducts]", [982, 984], "[navigate, redirect, userInfo]", [879, 881], "[navigate, userInfo]", [854, 856], [505, 507], "[logout, navigate, userInfo]", [1198, 1200], [1367, 1369], "[id, logout]", [1530, 1532], "[brandParam, categoryParam, loadProducts]", [524, 545], "[userInfo, productId, checkWishlistStatus]"]