[{"D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\index.js": "1", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\App.js": "2", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\reportWebVitals.js": "3", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\context\\productsContext.js": "4", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\context\\userContext.js": "5", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\context\\cartContext.js": "6", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\header.jsx": "7", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx": "8", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\footer.jsx": "9", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx": "10", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\ProtectedRoute.jsx": "11", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx": "12", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx": "13", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx": "14", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx": "15", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx": "16", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx": "17", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx": "18", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx": "19", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx": "20", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx": "21", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx": "22", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminDashboard.jsx": "23", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminUsers.jsx": "24", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminBrands.jsx": "25", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCategories.jsx": "26", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminOrders.jsx": "27", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminReviews.jsx": "28", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminProducts.jsx": "29", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\loader.jsx": "30", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\utils\\currency.js": "31", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\services\\httpService.js": "32", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\rating.jsx": "33", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx": "34", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx": "35", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\product.jsx": "36", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\brandCard.jsx": "37", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\categoryCard.jsx": "38", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\productsCarousel.jsx": "39", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\AdminRedirect.jsx": "40", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx": "41", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx": "42", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\message.jsx": "43", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx": "44", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx": "45", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminLayout.jsx": "46", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx": "47", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminHeader.jsx": "48", "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminSidebar.jsx": "49"}, {"size": 670, "mtime": 1751117666040, "results": "50", "hashOfConfig": "51"}, {"size": 5220, "mtime": 1751117666028, "results": "52", "hashOfConfig": "51"}, {"size": 375, "mtime": 1751117666060, "results": "53", "hashOfConfig": "51"}, {"size": 1648, "mtime": 1751117666039, "results": "54", "hashOfConfig": "51"}, {"size": 4611, "mtime": 1751117666040, "results": "55", "hashOfConfig": "51"}, {"size": 4424, "mtime": 1751117666039, "results": "56", "hashOfConfig": "51"}, {"size": 1846, "mtime": 1751117666035, "results": "57", "hashOfConfig": "51"}, {"size": 4762, "mtime": 1751117666058, "results": "58", "hashOfConfig": "51"}, {"size": 584, "mtime": 1751117666034, "results": "59", "hashOfConfig": "51"}, {"size": 2056, "mtime": 1751117666056, "results": "60", "hashOfConfig": "51"}, {"size": 572, "mtime": 1751117666030, "results": "61", "hashOfConfig": "51"}, {"size": 3186, "mtime": 1751117666056, "results": "62", "hashOfConfig": "51"}, {"size": 2832, "mtime": 1751117666059, "results": "63", "hashOfConfig": "51"}, {"size": 4196, "mtime": 1751117666045, "results": "64", "hashOfConfig": "51"}, {"size": 3480, "mtime": 1751117666059, "results": "65", "hashOfConfig": "51"}, {"size": 577, "mtime": 1751117666057, "results": "66", "hashOfConfig": "51"}, {"size": 3266, "mtime": 1751117666060, "results": "67", "hashOfConfig": "51"}, {"size": 5289, "mtime": 1751117666058, "results": "68", "hashOfConfig": "51"}, {"size": 6349, "mtime": 1751117666057, "results": "69", "hashOfConfig": "51"}, {"size": 1940, "mtime": 1751117666055, "results": "70", "hashOfConfig": "51"}, {"size": 1935, "mtime": 1751117666058, "results": "71", "hashOfConfig": "51"}, {"size": 4755, "mtime": 1751117666059, "results": "72", "hashOfConfig": "51"}, {"size": 7840, "mtime": 1751117666043, "results": "73", "hashOfConfig": "51"}, {"size": 13874, "mtime": 1751117666045, "results": "74", "hashOfConfig": "51"}, {"size": 8343, "mtime": 1751117666042, "results": "75", "hashOfConfig": "51"}, {"size": 8526, "mtime": 1751117666042, "results": "76", "hashOfConfig": "51"}, {"size": 10873, "mtime": 1751117666044, "results": "77", "hashOfConfig": "51"}, {"size": 8700, "mtime": 1751117666045, "results": "78", "hashOfConfig": "51"}, {"size": 14511, "mtime": 1751117666045, "results": "79", "hashOfConfig": "51"}, {"size": 413, "mtime": 1751117666035, "results": "80", "hashOfConfig": "51"}, {"size": 1359, "mtime": 1751117666061, "results": "81", "hashOfConfig": "51"}, {"size": 1624, "mtime": 1751117666061, "results": "82", "hashOfConfig": "51"}, {"size": 663, "mtime": 1751117666038, "results": "83", "hashOfConfig": "51"}, {"size": 1262, "mtime": 1751117666038, "results": "84", "hashOfConfig": "51"}, {"size": 3771, "mtime": 1751117666038, "results": "85", "hashOfConfig": "51"}, {"size": 1201, "mtime": 1751117666037, "results": "86", "hashOfConfig": "51"}, {"size": 760, "mtime": 1751117666033, "results": "87", "hashOfConfig": "51"}, {"size": 1024, "mtime": 1751117666033, "results": "88", "hashOfConfig": "51"}, {"size": 979, "mtime": 1751117666037, "results": "89", "hashOfConfig": "51"}, {"size": 727, "mtime": 1751117666029, "results": "90", "hashOfConfig": "51"}, {"size": 2595, "mtime": 1751117666036, "results": "91", "hashOfConfig": "51"}, {"size": 387, "mtime": 1751117666035, "results": "92", "hashOfConfig": "51"}, {"size": 239, "mtime": 1751117666036, "results": "93", "hashOfConfig": "51"}, {"size": 1574, "mtime": 1751117666034, "results": "94", "hashOfConfig": "51"}, {"size": 2631, "mtime": 1751161175854, "results": "95", "hashOfConfig": "51"}, {"size": 465, "mtime": 1751117666032, "results": "96", "hashOfConfig": "51"}, {"size": 1895, "mtime": 1751117666036, "results": "97", "hashOfConfig": "51"}, {"size": 3227, "mtime": 1751117666030, "results": "98", "hashOfConfig": "51"}, {"size": 3420, "mtime": 1751117666032, "results": "99", "hashOfConfig": "51"}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1sqkrsn", {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\index.js", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\App.js", ["247"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\reportWebVitals.js", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\context\\productsContext.js", ["248"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\context\\userContext.js", ["249", "250", "251", "252", "253", "254", "255", "256", "257"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\context\\cartContext.js", ["258", "259", "260"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\header.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx", ["261", "262"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\footer.jsx", ["263"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx", ["264", "265"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\ProtectedRoute.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx", ["266", "267"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx", ["268"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx", ["269", "270"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx", ["271", "272"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx", ["273"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx", ["274"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx", ["275", "276", "277"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx", ["278", "279", "280", "281"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx", ["282"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx", ["283", "284", "285", "286", "287", "288", "289", "290", "291"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminDashboard.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminUsers.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminBrands.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCategories.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminOrders.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminReviews.jsx", ["292"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminProducts.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\loader.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\utils\\currency.js", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\services\\httpService.js", ["293", "294"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\rating.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx", ["295"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx", ["296", "297"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\product.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\brandCard.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\categoryCard.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\productsCarousel.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\AdminRedirect.jsx", ["298"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx", ["299"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\message.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminLayout.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx", ["300", "301", "302"], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminHeader.jsx", [], [], "D:\\CHUONGTRINHHOC\\DATN\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminSidebar.jsx", [], [], {"ruleId": "303", "severity": 1, "message": "304", "line": 41, "column": 3, "nodeType": "305", "endLine": 41, "endColumn": 12, "suggestions": "306"}, {"ruleId": "307", "severity": 1, "message": "308", "line": 35, "column": 49, "nodeType": "309", "messageId": "310", "endLine": 35, "endColumn": 51}, {"ruleId": "311", "severity": 1, "message": "312", "line": 58, "column": 15, "nodeType": "305", "messageId": "313", "endLine": 58, "endColumn": 19}, {"ruleId": "303", "severity": 1, "message": "314", "line": 104, "column": 6, "nodeType": "315", "endLine": 104, "endColumn": 8, "suggestions": "316"}, {"ruleId": "303", "severity": 1, "message": "317", "line": 116, "column": 6, "nodeType": "315", "endLine": 116, "endColumn": 18, "suggestions": "318"}, {"ruleId": "307", "severity": 1, "message": "319", "line": 121, "column": 20, "nodeType": "309", "messageId": "310", "endLine": 121, "endColumn": 22}, {"ruleId": "307", "severity": 1, "message": "319", "line": 121, "column": 53, "nodeType": "309", "messageId": "310", "endLine": 121, "endColumn": 55}, {"ruleId": "307", "severity": 1, "message": "319", "line": 125, "column": 17, "nodeType": "309", "messageId": "310", "endLine": 125, "endColumn": 19}, {"ruleId": "307", "severity": 1, "message": "319", "line": 125, "column": 44, "nodeType": "309", "messageId": "310", "endLine": 125, "endColumn": 46}, {"ruleId": "307", "severity": 1, "message": "319", "line": 129, "column": 20, "nodeType": "309", "messageId": "310", "endLine": 129, "endColumn": 22}, {"ruleId": "307", "severity": 1, "message": "308", "line": 133, "column": 16, "nodeType": "309", "messageId": "310", "endLine": 133, "endColumn": 18}, {"ruleId": "307", "severity": 1, "message": "308", "line": 63, "column": 18, "nodeType": "309", "messageId": "310", "endLine": 63, "endColumn": 20}, {"ruleId": "307", "severity": 1, "message": "308", "line": 69, "column": 15, "nodeType": "309", "messageId": "310", "endLine": 69, "endColumn": 17}, {"ruleId": "307", "severity": 1, "message": "308", "line": 127, "column": 45, "nodeType": "309", "messageId": "310", "endLine": 127, "endColumn": 47}, {"ruleId": "303", "severity": 1, "message": "320", "line": 35, "column": 6, "nodeType": "315", "endLine": 35, "endColumn": 8, "suggestions": "321"}, {"ruleId": "307", "severity": 1, "message": "319", "line": 44, "column": 13, "nodeType": "309", "messageId": "310", "endLine": 44, "endColumn": 15}, {"ruleId": "322", "severity": 1, "message": "323", "line": 14, "column": 13, "nodeType": "324", "messageId": "325", "endLine": 14, "endColumn": 107, "fix": "326"}, {"ruleId": "303", "severity": 1, "message": "327", "line": 22, "column": 6, "nodeType": "315", "endLine": 22, "endColumn": 8, "suggestions": "328"}, {"ruleId": "307", "severity": 1, "message": "319", "line": 26, "column": 13, "nodeType": "309", "messageId": "310", "endLine": 26, "endColumn": 15}, {"ruleId": "311", "severity": 1, "message": "329", "line": 13, "column": 23, "nodeType": "305", "messageId": "313", "endLine": 13, "endColumn": 38}, {"ruleId": "303", "severity": 1, "message": "330", "line": 27, "column": 6, "nodeType": "315", "endLine": 27, "endColumn": 8, "suggestions": "331"}, {"ruleId": "303", "severity": 1, "message": "332", "line": 22, "column": 6, "nodeType": "315", "endLine": 22, "endColumn": 8, "suggestions": "333"}, {"ruleId": "311", "severity": 1, "message": "334", "line": 1, "column": 29, "nodeType": "305", "messageId": "313", "endLine": 1, "endColumn": 38}, {"ruleId": "307", "severity": 1, "message": "319", "line": 26, "column": 13, "nodeType": "309", "messageId": "310", "endLine": 26, "endColumn": 15}, {"ruleId": "335", "severity": 1, "message": "336", "line": 18, "column": 28, "nodeType": "305", "messageId": "337", "endLine": 18, "endColumn": 36}, {"ruleId": "303", "severity": 1, "message": "330", "line": 22, "column": 6, "nodeType": "315", "endLine": 22, "endColumn": 8, "suggestions": "338"}, {"ruleId": "303", "severity": 1, "message": "339", "line": 15, "column": 6, "nodeType": "315", "endLine": 15, "endColumn": 8, "suggestions": "340"}, {"ruleId": "303", "severity": 1, "message": "332", "line": 31, "column": 6, "nodeType": "315", "endLine": 31, "endColumn": 8, "suggestions": "341"}, {"ruleId": "311", "severity": 1, "message": "342", "line": 30, "column": 11, "nodeType": "305", "messageId": "313", "endLine": 30, "endColumn": 13}, {"ruleId": "307", "severity": 1, "message": "308", "line": 60, "column": 38, "nodeType": "309", "messageId": "310", "endLine": 60, "endColumn": 40}, {"ruleId": "307", "severity": 1, "message": "308", "line": 129, "column": 53, "nodeType": "309", "messageId": "310", "endLine": 129, "endColumn": 55}, {"ruleId": "307", "severity": 1, "message": "308", "line": 27, "column": 47, "nodeType": "309", "messageId": "310", "endLine": 27, "endColumn": 49}, {"ruleId": "307", "severity": 1, "message": "308", "line": 28, "column": 47, "nodeType": "309", "messageId": "310", "endLine": 28, "endColumn": 49}, {"ruleId": "303", "severity": 1, "message": "343", "line": 35, "column": 6, "nodeType": "315", "endLine": 35, "endColumn": 8, "suggestions": "344"}, {"ruleId": "307", "severity": 1, "message": "319", "line": 41, "column": 17, "nodeType": "309", "messageId": "310", "endLine": 41, "endColumn": 19}, {"ruleId": "307", "severity": 1, "message": "308", "line": 37, "column": 33, "nodeType": "309", "messageId": "310", "endLine": 37, "endColumn": 35}, {"ruleId": "311", "severity": 1, "message": "329", "line": 18, "column": 24, "nodeType": "305", "messageId": "313", "endLine": 18, "endColumn": 39}, {"ruleId": "303", "severity": 1, "message": "345", "line": 43, "column": 6, "nodeType": "315", "endLine": 43, "endColumn": 8, "suggestions": "346"}, {"ruleId": "307", "severity": 1, "message": "319", "line": 49, "column": 21, "nodeType": "309", "messageId": "310", "endLine": 49, "endColumn": 23}, {"ruleId": "307", "severity": 1, "message": "308", "line": 51, "column": 34, "nodeType": "309", "messageId": "310", "endLine": 51, "endColumn": 36}, {"ruleId": "307", "severity": 1, "message": "319", "line": 55, "column": 24, "nodeType": "309", "messageId": "310", "endLine": 55, "endColumn": 26}, {"ruleId": "307", "severity": 1, "message": "308", "line": 57, "column": 37, "nodeType": "309", "messageId": "310", "endLine": 57, "endColumn": 39}, {"ruleId": "307", "severity": 1, "message": "319", "line": 63, "column": 13, "nodeType": "309", "messageId": "310", "endLine": 63, "endColumn": 15}, {"ruleId": "307", "severity": 1, "message": "319", "line": 63, "column": 35, "nodeType": "309", "messageId": "310", "endLine": 63, "endColumn": 37}, {"ruleId": "307", "severity": 1, "message": "319", "line": 66, "column": 20, "nodeType": "309", "messageId": "310", "endLine": 66, "endColumn": 22}, {"ruleId": "311", "severity": 1, "message": "347", "line": 9, "column": 10, "nodeType": "305", "messageId": "313", "endLine": 9, "endColumn": 18}, {"ruleId": "307", "severity": 1, "message": "308", "line": 13, "column": 13, "nodeType": "309", "messageId": "310", "endLine": 13, "endColumn": 15}, {"ruleId": "348", "severity": 1, "message": "349", "line": 46, "column": 1, "nodeType": "350", "endLine": 53, "endColumn": 3}, {"ruleId": "311", "severity": 1, "message": "334", "line": 1, "column": 17, "nodeType": "305", "messageId": "313", "endLine": 1, "endColumn": 26}, {"ruleId": "307", "severity": 1, "message": "308", "line": 90, "column": 34, "nodeType": "309", "messageId": "310", "endLine": 90, "endColumn": 36}, {"ruleId": "307", "severity": 1, "message": "308", "line": 90, "column": 51, "nodeType": "309", "messageId": "310", "endLine": 90, "endColumn": 53}, {"ruleId": "311", "severity": 1, "message": "351", "line": 1, "column": 8, "nodeType": "305", "messageId": "313", "endLine": 1, "endColumn": 13}, {"ruleId": "307", "severity": 1, "message": "308", "line": 22, "column": 47, "nodeType": "309", "messageId": "310", "endLine": 22, "endColumn": 49}, {"ruleId": "311", "severity": 1, "message": "334", "line": 1, "column": 17, "nodeType": "305", "messageId": "313", "endLine": 1, "endColumn": 26}, {"ruleId": "311", "severity": 1, "message": "352", "line": 8, "column": 8, "nodeType": "305", "messageId": "313", "endLine": 8, "endColumn": 19}, {"ruleId": "311", "severity": 1, "message": "353", "line": 14, "column": 10, "nodeType": "305", "messageId": "313", "endLine": 14, "endColumn": 15}, "react-hooks/exhaustive-deps", "React Hook useEffect contains a call to 'setKeyword'. Without a list of dependencies, this can lead to an infinite chain of updates. To fix this, pass [keywordParam] as a second argument to the useEffect Hook.", "Identifier", ["354"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "no-unused-vars", "'data' is assigned a value but never used.", "unusedVar", "React Hook useEffect has missing dependencies: 'authTokens' and 'refresh'. Either include them or remove the dependency array.", "ArrayExpression", ["355"], "React Hook useEffect has a missing dependency: 'refresh'. Either include it or remove the dependency array.", ["356"], "Expected '!==' and instead saw '!='.", "React Hook useEffect has missing dependencies: 'id' and 'loadProduct'. Either include them or remove the dependency array.", ["357"], "react/jsx-no-target-blank", "Using target=\"_blank\" without rel=\"noreferrer\" (which implies rel=\"noopener\") is a security risk in older browsers: see https://mathiasbynens.github.io/rel-noopener/#recommendations", "JSXOpeningElement", "noTargetBlankWithoutNoreferrer", {"range": "358", "text": "359"}, "React Hook useEffect has a missing dependency: 'loadProducts'. Either include it or remove the dependency array.", ["360"], "'setSearchParams' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'navigate', 'redirect', and 'userInfo'. Either include them or remove the dependency array.", ["361"], "React Hook useEffect has missing dependencies: 'navigate' and 'userInfo'. Either include them or remove the dependency array.", ["362"], "'useEffect' is defined but never used.", "no-const-assign", "'redirect' is constant.", "const", ["363"], "React Hook useEffect has missing dependencies: 'logout', 'navigate', and 'userInfo'. Either include them or remove the dependency array.", ["364"], ["365"], "'id' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'id' and 'logout'. Either include them or remove the dependency array.", ["366"], "React Hook useEffect has missing dependencies: 'brandParam', 'categoryParam', and 'loadProducts'. Either include them or remove the dependency array.", ["367"], "'products' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'React' is defined but never used.", "'httpService' is defined but never used.", "'email' is assigned a value but never used.", {"desc": "368", "fix": "369"}, {"desc": "370", "fix": "371"}, {"desc": "370", "fix": "372"}, {"desc": "373", "fix": "374"}, [460, 460], " rel=\"noreferrer\"", {"desc": "375", "fix": "376"}, {"desc": "377", "fix": "378"}, {"desc": "379", "fix": "380"}, {"desc": "377", "fix": "381"}, {"desc": "382", "fix": "383"}, {"desc": "379", "fix": "384"}, {"desc": "385", "fix": "386"}, {"desc": "387", "fix": "388"}, "Add dependencies array: [keywordParam]", {"range": "389", "text": "390"}, "Update the dependencies array to be: [authTokens, refresh]", {"range": "391", "text": "392"}, {"range": "393", "text": "392"}, "Update the dependencies array to be: [id, loadProduct]", {"range": "394", "text": "395"}, "Update the dependencies array to be: [loadProducts]", {"range": "396", "text": "397"}, "Update the dependencies array to be: [navigate, redirect, userInfo]", {"range": "398", "text": "399"}, "Update the dependencies array to be: [navigate, userInfo]", {"range": "400", "text": "401"}, {"range": "402", "text": "399"}, "Update the dependencies array to be: [logout, navigate, userInfo]", {"range": "403", "text": "404"}, {"range": "405", "text": "401"}, "Update the dependencies array to be: [id, logout]", {"range": "406", "text": "407"}, "Update the dependencies array to be: [brandParam, categoryParam, loadProducts]", {"range": "408", "text": "409"}, [1876, 1876], ", [keywordParam]", [2899, 2901], "[authTokens, refresh]", [3287, 3299], [1092, 1094], "[id, loadProduct]", [860, 862], "[loadProducts]", [982, 984], "[navigate, redirect, userInfo]", [879, 881], "[navigate, userInfo]", [854, 856], [505, 507], "[logout, navigate, userInfo]", [1198, 1200], [1367, 1369], "[id, logout]", [1530, 1532], "[brandParam, categoryParam, loadProducts]"]