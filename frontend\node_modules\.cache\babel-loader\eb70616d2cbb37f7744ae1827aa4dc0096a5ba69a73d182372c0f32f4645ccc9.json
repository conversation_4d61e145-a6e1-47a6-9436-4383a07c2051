{"ast": null, "code": "var _jsxFileName = \"D:\\\\CHUONGTRINHHOC\\\\DATN\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\wishlistButton.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useContext } from \"react\";\nimport { Button } from \"react-bootstrap\";\nimport UserContext from \"../context/userContext\";\nimport httpService from \"../services/httpService\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction WishlistButton(_ref) {\n  _s();\n  let {\n    productId,\n    size = \"sm\",\n    className = \"\"\n  } = _ref;\n  const [inWishlist, setInWishlist] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const {\n    userInfo\n  } = useContext(UserContext);\n  useEffect(() => {\n    if (userInfo && productId) {\n      checkWishlistStatus();\n    }\n  }, [userInfo, productId]);\n  const checkWishlistStatus = async () => {\n    try {\n      const response = await httpService.get(`/api/products/${productId}/wishlist/status/`);\n      setInWishlist(response.data.in_wishlist);\n    } catch (error) {\n      console.error(\"Error checking wishlist status:\", error);\n    }\n  };\n  const toggleWishlist = async () => {\n    if (!userInfo) {\n      alert(\"Vui lòng đăng nhập để thêm sản phẩm yêu thích\");\n      return;\n    }\n    setLoading(true);\n    try {\n      const response = await httpService.post(`/api/products/${productId}/wishlist/toggle/`);\n      setInWishlist(response.data.in_wishlist);\n\n      // Show notification\n      if (response.data.in_wishlist) {\n        alert(\"Đã thêm vào danh sách yêu thích!\");\n      } else {\n        alert(\"Đã xóa khỏi danh sách yêu thích!\");\n      }\n    } catch (error) {\n      console.error(\"Error toggling wishlist:\", error);\n      alert(\"Có lỗi xảy ra. Vui lòng thử lại!\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (!userInfo) {\n    return null; // Don't show button if user not logged in\n  }\n\n  return /*#__PURE__*/_jsxDEV(Button, {\n    variant: inWishlist ? \"danger\" : \"outline-danger\",\n    size: size,\n    onClick: toggleWishlist,\n    disabled: loading,\n    className: `wishlist-btn ${className}`,\n    title: inWishlist ? \"Xóa khỏi yêu thích\" : \"Thêm vào yêu thích\",\n    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n      className: `fas fa-heart ${loading ? \"fa-spin\" : \"\"}`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), size !== \"sm\" && /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"ms-2\",\n      children: inWishlist ? \"Đã yêu thích\" : \"Yêu thích\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 60,\n    columnNumber: 5\n  }, this);\n}\n_s(WishlistButton, \"QFhCHAQAU4evgiuy6Xp9jHwJlaA=\");\n_c = WishlistButton;\nexport default WishlistButton;\nvar _c;\n$RefreshReg$(_c, \"WishlistButton\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "<PERSON><PERSON>", "UserContext", "httpService", "jsxDEV", "_jsxDEV", "WishlistButton", "_ref", "_s", "productId", "size", "className", "inWishlist", "setInWishlist", "loading", "setLoading", "userInfo", "checkWishlistStatus", "response", "get", "data", "in_wishlist", "error", "console", "toggleWishlist", "alert", "post", "variant", "onClick", "disabled", "title", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/CHUONGTRINHHOC/DATN/Python-KienTap-/frontend/src/components/wishlistButton.jsx"], "sourcesContent": ["import React, { useState, useEffect, useContext } from \"react\";\nimport { Button } from \"react-bootstrap\";\nimport UserContext from \"../context/userContext\";\nimport httpService from \"../services/httpService\";\n\nfunction WishlistButton({ productId, size = \"sm\", className = \"\" }) {\n  const [inWishlist, setInWishlist] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const { userInfo } = useContext(UserContext);\n\n  useEffect(() => {\n    if (userInfo && productId) {\n      checkWishlistStatus();\n    }\n  }, [userInfo, productId]);\n\n  const checkWishlistStatus = async () => {\n    try {\n      const response = await httpService.get(\n        `/api/products/${productId}/wishlist/status/`\n      );\n      setInWishlist(response.data.in_wishlist);\n    } catch (error) {\n      console.error(\"Error checking wishlist status:\", error);\n    }\n  };\n\n  const toggleWishlist = async () => {\n    if (!userInfo) {\n      alert(\"Vui lòng đăng nhập để thêm sản phẩm yêu thích\");\n      return;\n    }\n\n    setLoading(true);\n    try {\n      const response = await httpService.post(\n        `/api/products/${productId}/wishlist/toggle/`\n      );\n      setInWishlist(response.data.in_wishlist);\n      \n      // Show notification\n      if (response.data.in_wishlist) {\n        alert(\"Đã thêm vào danh sách yêu thích!\");\n      } else {\n        alert(\"Đã xóa khỏi danh sách yêu thích!\");\n      }\n    } catch (error) {\n      console.error(\"Error toggling wishlist:\", error);\n      alert(\"Có lỗi xảy ra. Vui lòng thử lại!\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (!userInfo) {\n    return null; // Don't show button if user not logged in\n  }\n\n  return (\n    <Button\n      variant={inWishlist ? \"danger\" : \"outline-danger\"}\n      size={size}\n      onClick={toggleWishlist}\n      disabled={loading}\n      className={`wishlist-btn ${className}`}\n      title={inWishlist ? \"Xóa khỏi yêu thích\" : \"Thêm vào yêu thích\"}\n    >\n      <i className={`fas fa-heart ${loading ? \"fa-spin\" : \"\"}`}></i>\n      {size !== \"sm\" && (\n        <span className=\"ms-2\">\n          {inWishlist ? \"Đã yêu thích\" : \"Yêu thích\"}\n        </span>\n      )}\n    </Button>\n  );\n}\n\nexport default WishlistButton;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,MAAM,QAAQ,iBAAiB;AACxC,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,WAAW,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,SAASC,cAAcA,CAAAC,IAAA,EAA6C;EAAAC,EAAA;EAAA,IAA5C;IAAEC,SAAS;IAAEC,IAAI,GAAG,IAAI;IAAEC,SAAS,GAAG;EAAG,CAAC,GAAAJ,IAAA;EAChE,MAAM,CAACK,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAEkB;EAAS,CAAC,GAAGhB,UAAU,CAACE,WAAW,CAAC;EAE5CH,SAAS,CAAC,MAAM;IACd,IAAIiB,QAAQ,IAAIP,SAAS,EAAE;MACzBQ,mBAAmB,EAAE;IACvB;EACF,CAAC,EAAE,CAACD,QAAQ,EAAEP,SAAS,CAAC,CAAC;EAEzB,MAAMQ,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMf,WAAW,CAACgB,GAAG,CACnC,iBAAgBV,SAAU,mBAAkB,CAC9C;MACDI,aAAa,CAACK,QAAQ,CAACE,IAAI,CAACC,WAAW,CAAC;IAC1C,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD;EACF,CAAC;EAED,MAAME,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAACR,QAAQ,EAAE;MACbS,KAAK,CAAC,+CAA+C,CAAC;MACtD;IACF;IAEAV,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMf,WAAW,CAACuB,IAAI,CACpC,iBAAgBjB,SAAU,mBAAkB,CAC9C;MACDI,aAAa,CAACK,QAAQ,CAACE,IAAI,CAACC,WAAW,CAAC;;MAExC;MACA,IAAIH,QAAQ,CAACE,IAAI,CAACC,WAAW,EAAE;QAC7BI,KAAK,CAAC,kCAAkC,CAAC;MAC3C,CAAC,MAAM;QACLA,KAAK,CAAC,kCAAkC,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDG,KAAK,CAAC,kCAAkC,CAAC;IAC3C,CAAC,SAAS;MACRV,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAI,CAACC,QAAQ,EAAE;IACb,OAAO,IAAI,CAAC,CAAC;EACf;;EAEA,oBACEX,OAAA,CAACJ,MAAM;IACL0B,OAAO,EAAEf,UAAU,GAAG,QAAQ,GAAG,gBAAiB;IAClDF,IAAI,EAAEA,IAAK;IACXkB,OAAO,EAAEJ,cAAe;IACxBK,QAAQ,EAAEf,OAAQ;IAClBH,SAAS,EAAG,gBAAeA,SAAU,EAAE;IACvCmB,KAAK,EAAElB,UAAU,GAAG,oBAAoB,GAAG,oBAAqB;IAAAmB,QAAA,gBAEhE1B,OAAA;MAAGM,SAAS,EAAG,gBAAeG,OAAO,GAAG,SAAS,GAAG,EAAG;IAAE;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAK,EAC7DzB,IAAI,KAAK,IAAI,iBACZL,OAAA;MAAMM,SAAS,EAAC,MAAM;MAAAoB,QAAA,EACnBnB,UAAU,GAAG,cAAc,GAAG;IAAW;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAE7C;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACM;AAEb;AAAC3B,EAAA,CAtEQF,cAAc;AAAA8B,EAAA,GAAd9B,cAAc;AAwEvB,eAAeA,cAAc;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}