{"name": "@babel/plugin-transform-arrow-functions", "version": "7.20.7", "description": "Compile ES2015 arrow functions to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-arrow-functions"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-arrow-functions", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.20.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.20.7", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/traverse": "^7.20.7", "@babel/types": "^7.20.7"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}