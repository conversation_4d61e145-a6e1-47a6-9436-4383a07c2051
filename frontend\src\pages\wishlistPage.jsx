import React, { useState, useEffect, useContext } from "react";
import { <PERSON>, Col, Card, Button, Container } from "react-bootstrap";
import { Link } from "react-router-dom";
import UserContext from "../context/userContext";
import httpService from "../services/httpService";
import Loader from "../components/loader";
import Message from "../components/message";
import Rating from "../components/rating";
import { formatVND } from "../utils/currency";

function WishlistPage() {
  const [wishlist, setWishlist] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const { userInfo } = useContext(UserContext);

  useEffect(() => {
    if (userInfo) {
      fetchWishlist();
    } else {
      setLoading(false);
      setError("Vui lòng đăng nhập để xem danh sách yêu thích");
    }
  }, [userInfo]);

  const fetchWishlist = async () => {
    try {
      const response = await httpService.get("/api/wishlist/");
      setWishlist(response.data.results || response.data);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching wishlist:", error);
      setError("Không thể tải danh sách yêu thích");
      setLoading(false);
    }
  };

  const removeFromWishlist = async (wishlistId) => {
    try {
      await httpService.delete(`/api/wishlist/${wishlistId}/`);
      setWishlist(wishlist.filter(item => item.id !== wishlistId));
      alert("Đã xóa khỏi danh sách yêu thích!");
    } catch (error) {
      console.error("Error removing from wishlist:", error);
      alert("Có lỗi xảy ra. Vui lòng thử lại!");
    }
  };

  if (loading) return <Loader />;

  if (error) {
    return (
      <Container>
        <Message variant="danger">
          <h4>{error}</h4>
        </Message>
      </Container>
    );
  }

  return (
    <Container className="wishlist-page">
      <Row>
        <Col>
          <h1 className="mb-4">
            <i className="fas fa-heart text-danger me-2"></i>
            Sản phẩm yêu thích
          </h1>
          
          {wishlist.length === 0 ? (
            <Card className="text-center p-5">
              <Card.Body>
                <i className="fas fa-heart-broken fa-3x text-muted mb-3"></i>
                <h4>Danh sách yêu thích trống</h4>
                <p className="text-muted">
                  Bạn chưa có sản phẩm yêu thích nào. 
                  Hãy khám phá và thêm những sản phẩm bạn thích!
                </p>
                <Link to="/" className="btn btn-primary">
                  Khám phá sản phẩm
                </Link>
              </Card.Body>
            </Card>
          ) : (
            <Row>
              {wishlist.map((item) => (
                <Col key={item.id} sm={12} md={6} lg={4} xl={3} className="mb-4">
                  <Card className="h-100">
                    <Link to={`/products/${item.product.id}`}>
                      <Card.Img 
                        variant="top" 
                        src={item.product.image} 
                        alt={item.product.name}
                        style={{ height: "200px", objectFit: "cover" }}
                      />
                    </Link>
                    <Card.Body className="d-flex flex-column">
                      <Link 
                        to={`/products/${item.product.id}`}
                        className="text-decoration-none"
                      >
                        <Card.Title className="text-truncate">
                          {item.product.name}
                        </Card.Title>
                      </Link>
                      
                      <div className="mb-2">
                        <Rating
                          value={item.product.rating}
                          text={`${item.product.numReviews} reviews`}
                          color={"#f8e825"}
                        />
                      </div>
                      
                      <Card.Text className="h5 text-primary">
                        {formatVND(item.product.price)}
                      </Card.Text>
                      
                      <div className="mt-auto">
                        <Row>
                          <Col>
                            <Link 
                              to={`/products/${item.product.id}`}
                              className="btn btn-primary btn-sm w-100"
                            >
                              Xem chi tiết
                            </Link>
                          </Col>
                          <Col xs="auto">
                            <Button
                              variant="outline-danger"
                              size="sm"
                              onClick={() => removeFromWishlist(item.id)}
                              title="Xóa khỏi yêu thích"
                            >
                              <i className="fas fa-trash"></i>
                            </Button>
                          </Col>
                        </Row>
                      </div>
                    </Card.Body>
                    <Card.Footer className="text-muted small">
                      Thêm vào: {new Date(item.created_at).toLocaleDateString('vi-VN')}
                    </Card.Footer>
                  </Card>
                </Col>
              ))}
            </Row>
          )}
        </Col>
      </Row>
    </Container>
  );
}

export default WishlistPage;
