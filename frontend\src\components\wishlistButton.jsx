import React, { useState, useEffect, useContext } from "react";
import { Button } from "react-bootstrap";
import UserContext from "../context/userContext";
import httpService from "../services/httpService";

function WishlistButton({ productId, size = "sm", className = "" }) {
  const [inWishlist, setInWishlist] = useState(false);
  const [loading, setLoading] = useState(false);
  const { userInfo } = useContext(UserContext);

  useEffect(() => {
    if (userInfo && productId) {
      checkWishlistStatus();
    }
  }, [userInfo, productId]);

  const checkWishlistStatus = async () => {
    try {
      const response = await httpService.get(
        `/api/products/${productId}/wishlist/status/`
      );
      setInWishlist(response.data.in_wishlist);
    } catch (error) {
      console.error("Error checking wishlist status:", error);
    }
  };

  const toggleWishlist = async () => {
    if (!userInfo) {
      alert("Vui lòng đăng nhập để thêm sản phẩm yêu thích");
      return;
    }

    setLoading(true);
    try {
      const response = await httpService.post(
        `/api/products/${productId}/wishlist/toggle/`
      );
      setInWishlist(response.data.in_wishlist);
      
      // Show notification
      if (response.data.in_wishlist) {
        alert("Đã thêm vào danh sách yêu thích!");
      } else {
        alert("Đã xóa khỏi danh sách yêu thích!");
      }
    } catch (error) {
      console.error("Error toggling wishlist:", error);
      alert("Có lỗi xảy ra. Vui lòng thử lại!");
    } finally {
      setLoading(false);
    }
  };

  if (!userInfo) {
    return null; // Don't show button if user not logged in
  }

  return (
    <Button
      variant={inWishlist ? "danger" : "outline-danger"}
      size={size}
      onClick={toggleWishlist}
      disabled={loading}
      className={`wishlist-btn ${className}`}
      title={inWishlist ? "Xóa khỏi yêu thích" : "Thêm vào yêu thích"}
    >
      <i className={`fas fa-heart ${loading ? "fa-spin" : ""}`}></i>
      {size !== "sm" && (
        <span className="ms-2">
          {inWishlist ? "Đã yêu thích" : "Yêu thích"}
        </span>
      )}
    </Button>
  );
}

export default WishlistButton;
