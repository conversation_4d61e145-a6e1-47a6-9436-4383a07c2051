{"name": "eslint-plugin-react", "version": "7.32.2", "author": "<PERSON><PERSON> <<EMAIL>>", "description": "React specific linting rules for ESLint", "main": "index.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prelint": "npm run lint:docs", "lint:docs": "markdownlint \"**/*.md\"", "postlint:docs": "npm run update:eslint-docs -- --check", "lint": "eslint .", "postlint": "npm run type-check", "pretest": "npm run lint", "test": "npm run unit-test", "posttest": "aud --production", "type-check": "tsc", "unit-test": "istanbul cover node_modules/mocha/bin/_mocha tests/lib/**/*.js tests/util/**/*.js tests/index.js", "update:eslint-docs": "eslint-doc-generator"}, "repository": {"type": "git", "url": "https://github.com/jsx-eslint/eslint-plugin-react"}, "homepage": "https://github.com/jsx-eslint/eslint-plugin-react", "bugs": "https://github.com/jsx-eslint/eslint-plugin-react/issues", "dependencies": {"array-includes": "^3.1.6", "array.prototype.flatmap": "^1.3.1", "array.prototype.tosorted": "^1.1.1", "doctrine": "^2.1.0", "estraverse": "^5.3.0", "jsx-ast-utils": "^2.4.1 || ^3.0.0", "minimatch": "^3.1.2", "object.entries": "^1.1.6", "object.fromentries": "^2.0.6", "object.hasown": "^1.1.2", "object.values": "^1.1.6", "prop-types": "^15.8.1", "resolve": "^2.0.0-next.4", "semver": "^6.3.0", "string.prototype.matchall": "^4.0.8"}, "devDependencies": {"@babel/core": "^7.20.12", "@babel/eslint-parser": "^7.19.1", "@babel/plugin-syntax-decorators": "^7.19.0", "@babel/plugin-syntax-do-expressions": "^7.18.6", "@babel/plugin-syntax-function-bind": "^7.18.6", "@babel/preset-react": "^7.18.6", "@types/eslint": "=7.2.10", "@types/estree": "0.0.52", "@types/node": "^4.9.5", "@typescript-eslint/parser": "^2.34.0 || ^3.10.1 || ^4.0.0 || ^5.0.0", "aud": "^2.0.2", "babel-eslint": "^8 || ^9 || ^10.1.0", "eslint": "^3 || ^4 || ^5 || ^6 || ^7 || ^8", "eslint-config-airbnb-base": "^15.0.0", "eslint-doc-generator": "^1.4.2", "eslint-plugin-eslint-plugin": "^2.3.0 || ^3.5.3 || ^4.0.1 || ^5.0.5", "eslint-plugin-import": "^2.26.0", "eslint-remote-tester": "^3.0.0", "eslint-remote-tester-repositories": "^1.0.0", "eslint-scope": "^3.7.3", "espree": "^3.5.4", "istanbul": "^0.4.5", "ls-engines": "^0.8.0", "markdownlint-cli": "^0.8.0 || ^0.32.2", "mocha": "^5.2.0", "npmignore": "^0.3.0", "sinon": "^7.5.0", "typescript": "^3.9.9", "typescript-eslint-parser": "^20.1.1"}, "peerDependencies": {"eslint": "^3 || ^4 || ^5 || ^6 || ^7 || ^8"}, "engines": {"node": ">=4"}, "keywords": ["eslint", "eslint-plugin", "eslintplugin", "react"], "license": "MIT", "publishConfig": {"ignore": [".github/", "!lib", "docs/", "test/", "tests/", "*.md", "*.config.js", ".eslint-doc-generatorrc.js", ".eslintrc", ".editorconfig", "tsconfig.json", ".markdownlint*"]}}