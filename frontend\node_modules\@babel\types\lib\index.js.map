{"version": 3, "names": ["react", "isReactComponent", "isCompatTag", "buildChildren"], "sources": ["../src/index.ts"], "sourcesContent": ["import isReactComponent from \"./validators/react/isReactComponent\";\nimport isCompatTag from \"./validators/react/isCompatTag\";\nimport buildChildren from \"./builders/react/buildChildren\";\n\n// asserts\nexport { default as assertNode } from \"./asserts/assertNode\";\nexport * from \"./asserts/generated\";\n\n// builders\nexport { default as createTypeAnnotationBasedOnTypeof } from \"./builders/flow/createTypeAnnotationBasedOnTypeof\";\n/** @deprecated use createFlowUnionType instead */\nexport { default as createUnionTypeAnnotation } from \"./builders/flow/createFlowUnionType\";\nexport { default as createFlowUnionType } from \"./builders/flow/createFlowUnionType\";\nexport { default as createTSUnionType } from \"./builders/typescript/createTSUnionType\";\nexport * from \"./builders/generated\";\nexport * from \"./builders/generated/uppercase\";\n\n// clone\nexport { default as cloneNode } from \"./clone/cloneNode\";\nexport { default as clone } from \"./clone/clone\";\nexport { default as cloneDeep } from \"./clone/cloneDeep\";\nexport { default as cloneDeepWithoutLoc } from \"./clone/cloneDeepWithoutLoc\";\nexport { default as cloneWithoutLoc } from \"./clone/cloneWithoutLoc\";\n\n// comments\nexport { default as addComment } from \"./comments/addComment\";\nexport { default as addComments } from \"./comments/addComments\";\nexport { default as inheritInnerComments } from \"./comments/inheritInnerComments\";\nexport { default as inheritLeadingComments } from \"./comments/inheritLeadingComments\";\nexport { default as inheritsComments } from \"./comments/inheritsComments\";\nexport { default as inheritTrailingComments } from \"./comments/inheritTrailingComments\";\nexport { default as removeComments } from \"./comments/removeComments\";\n\n// constants\nexport * from \"./constants/generated\";\nexport * from \"./constants\";\n\n// converters\nexport { default as ensureBlock } from \"./converters/ensureBlock\";\nexport { default as toBindingIdentifierName } from \"./converters/toBindingIdentifierName\";\nexport { default as toBlock } from \"./converters/toBlock\";\nexport { default as toComputedKey } from \"./converters/toComputedKey\";\nexport { default as toExpression } from \"./converters/toExpression\";\nexport { default as toIdentifier } from \"./converters/toIdentifier\";\nexport { default as toKeyAlias } from \"./converters/toKeyAlias\";\nexport { default as toSequenceExpression } from \"./converters/toSequenceExpression\";\nexport { default as toStatement } from \"./converters/toStatement\";\nexport { default as valueToNode } from \"./converters/valueToNode\";\n\n// definitions\nexport * from \"./definitions\";\n\n// modifications\nexport { default as appendToMemberExpression } from \"./modifications/appendToMemberExpression\";\nexport { default as inherits } from \"./modifications/inherits\";\nexport { default as prependToMemberExpression } from \"./modifications/prependToMemberExpression\";\nexport {\n  default as removeProperties,\n  type Options as RemovePropertiesOptions,\n} from \"./modifications/removeProperties\";\nexport { default as removePropertiesDeep } from \"./modifications/removePropertiesDeep\";\nexport { default as removeTypeDuplicates } from \"./modifications/flow/removeTypeDuplicates\";\n\n// retrievers\nexport { default as getBindingIdentifiers } from \"./retrievers/getBindingIdentifiers\";\nexport { default as getOuterBindingIdentifiers } from \"./retrievers/getOuterBindingIdentifiers\";\n\n// traverse\nexport { default as traverse } from \"./traverse/traverse\";\nexport * from \"./traverse/traverse\";\nexport { default as traverseFast } from \"./traverse/traverseFast\";\n\n// utils\nexport { default as shallowEqual } from \"./utils/shallowEqual\";\n\n// validators\nexport { default as is } from \"./validators/is\";\nexport { default as isBinding } from \"./validators/isBinding\";\nexport { default as isBlockScoped } from \"./validators/isBlockScoped\";\nexport { default as isImmutable } from \"./validators/isImmutable\";\nexport { default as isLet } from \"./validators/isLet\";\nexport { default as isNode } from \"./validators/isNode\";\nexport { default as isNodesEquivalent } from \"./validators/isNodesEquivalent\";\nexport { default as isPlaceholderType } from \"./validators/isPlaceholderType\";\nexport { default as isReferenced } from \"./validators/isReferenced\";\nexport { default as isScope } from \"./validators/isScope\";\nexport { default as isSpecifierDefault } from \"./validators/isSpecifierDefault\";\nexport { default as isType } from \"./validators/isType\";\nexport { default as isValidES3Identifier } from \"./validators/isValidES3Identifier\";\nexport { default as isValidIdentifier } from \"./validators/isValidIdentifier\";\nexport { default as isVar } from \"./validators/isVar\";\nexport { default as matchesPattern } from \"./validators/matchesPattern\";\nexport { default as validate } from \"./validators/validate\";\nexport { default as buildMatchMemberExpression } from \"./validators/buildMatchMemberExpression\";\nexport * from \"./validators/generated\";\n\n// react\nexport const react = {\n  isReactComponent,\n  isCompatTag,\n  buildChildren,\n};\n\nexport * from \"./ast-types/generated\";\n\n// this is used by @babel/traverse to warn about deprecated visitors\nexport { default as __internal__deprecationWarning } from \"./utils/deprecationWarning\";\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAGA;AACA;AAAA;EAAA;EAAA;EAAA;EAAA;IAAA;IAAA;MAAA;IAAA;EAAA;AAAA;AAGA;AAEA;AAEA;AACA;AAAA;EAAA;EAAA;EAAA;EAAA;IAAA;IAAA;MAAA;IAAA;EAAA;AAAA;AACA;AAAA;EAAA;EAAA;EAAA;EAAA;IAAA;IAAA;MAAA;IAAA;EAAA;AAAA;AAGA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAAA;EAAA;EAAA;EAAA;EAAA;IAAA;IAAA;MAAA;IAAA;EAAA;AAAA;AACA;AAAA;EAAA;EAAA;EAAA;EAAA;IAAA;IAAA;MAAA;IAAA;EAAA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAAA;EAAA;EAAA;EAAA;EAAA;IAAA;IAAA;MAAA;IAAA;EAAA;AAAA;AAGA;AACA;AACA;AACA;AAIA;AACA;AAGA;AACA;AAGA;AACA;EAAA;EAAA;EAAA;EAAA;IAAA;IAAA;MAAA;IAAA;EAAA;AAAA;AACA;AAGA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;EAAA;EAAA;EAAA;EAAA;IAAA;IAAA;MAAA;IAAA;EAAA;AAAA;AASA;AAAA;EAAA;EAAA;EAAA;EAAA;IAAA;IAAA;MAAA;IAAA;EAAA;AAAA;AAGA;AATO,MAAMA,KAAK,GAAG;EACnBC,gBAAgB,EAAhBA,yBAAgB;EAChBC,WAAW,EAAXA,oBAAW;EACXC,aAAa,EAAbA;AACF,CAAC;AAAC"}