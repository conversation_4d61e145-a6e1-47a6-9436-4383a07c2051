{"ast": null, "code": "var _jsxFileName = \"D:\\\\CHUONGTRINHHOC\\\\DATN\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\product.jsx\";\nimport React from \"react\";\nimport { Card } from \"react-bootstrap\";\nimport Rating from \"./rating\";\nimport WishlistButton from \"./wishlistButton\";\nimport { Link } from \"react-router-dom\";\nimport { formatVND } from \"../utils/currency\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Product(_ref) {\n  let {\n    product\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: \"my-3 p-3 rounded position-relative\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"position-absolute\",\n      style: {\n        top: \"10px\",\n        right: \"10px\",\n        zIndex: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(WishlistButton, {\n        productId: product.id,\n        size: \"sm\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Link, {\n      to: `/products/${product.id}`,\n      onClick: () => {\n        window.scrollTo(0, 0);\n      },\n      children: /*#__PURE__*/_jsxDEV(Card.Img, {\n        src: product.image\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: `/products/${product.id}`,\n        className: \"text-decoration-none\",\n        onClick: () => {\n          window.scrollTo(0, 0);\n        },\n        children: /*#__PURE__*/_jsxDEV(Card.Title, {\n          as: \"div\",\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: product.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card.Text, {\n        as: \"div\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-3\",\n          children: /*#__PURE__*/_jsxDEV(Rating, {\n            value: product.rating,\n            text: `${product.numReviews} reviews`,\n            color: \"#f8e825\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card.Text, {\n        as: \"h3\",\n        children: formatVND(product.price)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n}\n_c = Product;\nexport default Product;\nvar _c;\n$RefreshReg$(_c, \"Product\");", "map": {"version": 3, "names": ["React", "Card", "Rating", "WishlistButton", "Link", "formatVND", "jsxDEV", "_jsxDEV", "Product", "_ref", "product", "className", "children", "style", "top", "right", "zIndex", "productId", "id", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "onClick", "window", "scrollTo", "Img", "src", "image", "Body", "Title", "as", "name", "Text", "value", "rating", "text", "numReviews", "color", "price", "_c", "$RefreshReg$"], "sources": ["D:/CHUONGTRINHHOC/DATN/Python-KienTap-/frontend/src/components/product.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Card } from \"react-bootstrap\";\r\nimport Rating from \"./rating\";\r\nimport WishlistButton from \"./wishlistButton\";\r\nimport { Link } from \"react-router-dom\";\r\nimport { formatVND } from \"../utils/currency\";\r\n\r\nfunction Product({ product }) {\r\n  return (\r\n    <Card className=\"my-3 p-3 rounded position-relative\">\r\n      {/* Wishlist button positioned at top-right */}\r\n      <div className=\"position-absolute\" style={{ top: \"10px\", right: \"10px\", zIndex: 1 }}>\r\n        <WishlistButton productId={product.id} size=\"sm\" />\r\n      </div>\r\n\r\n      <Link\r\n        to={`/products/${product.id}`}\r\n        onClick={() => {\r\n          window.scrollTo(0, 0);\r\n        }}\r\n      >\r\n        <Card.Img src={product.image} />\r\n      </Link>\r\n      <Card.Body>\r\n        <Link\r\n          to={`/products/${product.id}`}\r\n          className=\"text-decoration-none\"\r\n          onClick={() => {\r\n            window.scrollTo(0, 0);\r\n          }}\r\n        >\r\n          <Card.Title as=\"div\">\r\n            <strong>{product.name}</strong>\r\n          </Card.Title>\r\n        </Link>\r\n        <Card.Text as=\"div\">\r\n          <div className=\"my-3\">\r\n            <Rating\r\n              value={product.rating}\r\n              text={`${product.numReviews} reviews`}\r\n              color={\"#f8e825\"}\r\n            />\r\n          </div>\r\n        </Card.Text>\r\n        <Card.Text as=\"h3\">{formatVND(product.price)}</Card.Text>\r\n      </Card.Body>\r\n    </Card>\r\n  );\r\n}\r\n\r\nexport default Product;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,iBAAiB;AACtC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,SAAS,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,SAASC,OAAOA,CAAAC,IAAA,EAAc;EAAA,IAAb;IAAEC;EAAQ,CAAC,GAAAD,IAAA;EAC1B,oBACEF,OAAA,CAACN,IAAI;IAACU,SAAS,EAAC,oCAAoC;IAAAC,QAAA,gBAElDL,OAAA;MAAKI,SAAS,EAAC,mBAAmB;MAACE,KAAK,EAAE;QAAEC,GAAG,EAAE,MAAM;QAAEC,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAClFL,OAAA,CAACJ,cAAc;QAACc,SAAS,EAAEP,OAAO,CAACQ,EAAG;QAACC,IAAI,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAG;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAC/C,eAENhB,OAAA,CAACH,IAAI;MACHoB,EAAE,EAAG,aAAYd,OAAO,CAACQ,EAAG,EAAE;MAC9BO,OAAO,EAAEA,CAAA,KAAM;QACbC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;MACvB,CAAE;MAAAf,QAAA,eAEFL,OAAA,CAACN,IAAI,CAAC2B,GAAG;QAACC,GAAG,EAAEnB,OAAO,CAACoB;MAAM;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAG;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAC3B,eACPhB,OAAA,CAACN,IAAI,CAAC8B,IAAI;MAAAnB,QAAA,gBACRL,OAAA,CAACH,IAAI;QACHoB,EAAE,EAAG,aAAYd,OAAO,CAACQ,EAAG,EAAE;QAC9BP,SAAS,EAAC,sBAAsB;QAChCc,OAAO,EAAEA,CAAA,KAAM;UACbC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;QACvB,CAAE;QAAAf,QAAA,eAEFL,OAAA,CAACN,IAAI,CAAC+B,KAAK;UAACC,EAAE,EAAC,KAAK;UAAArB,QAAA,eAClBL,OAAA;YAAAK,QAAA,EAASF,OAAO,CAACwB;UAAI;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAU;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACpB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACR,eACPhB,OAAA,CAACN,IAAI,CAACkC,IAAI;QAACF,EAAE,EAAC,KAAK;QAAArB,QAAA,eACjBL,OAAA;UAAKI,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBL,OAAA,CAACL,MAAM;YACLkC,KAAK,EAAE1B,OAAO,CAAC2B,MAAO;YACtBC,IAAI,EAAG,GAAE5B,OAAO,CAAC6B,UAAW,UAAU;YACtCC,KAAK,EAAE;UAAU;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACI,eACZhB,OAAA,CAACN,IAAI,CAACkC,IAAI;QAACF,EAAE,EAAC,IAAI;QAAArB,QAAA,EAAEP,SAAS,CAACK,OAAO,CAAC+B,KAAK;MAAC;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAa;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAC/C;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACP;AAEX;AAACmB,EAAA,GAzCQlC,OAAO;AA2ChB,eAAeA,OAAO;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}