import React from "react";
import { Card } from "react-bootstrap";
import Rating from "./rating";
import WishlistButton from "./wishlistButton";
import { Link } from "react-router-dom";
import { formatVND } from "../utils/currency";

function Product({ product }) {
  return (
    <Card className="my-3 p-3 rounded position-relative">
      {/* Wishlist button positioned at top-right */}
      <div className="position-absolute" style={{ top: "10px", right: "10px", zIndex: 1 }}>
        <WishlistButton productId={product.id} size="sm" />
      </div>

      <Link
        to={`/products/${product.id}`}
        onClick={() => {
          window.scrollTo(0, 0);
        }}
      >
        <Card.Img src={product.image} />
      </Link>
      <Card.Body>
        <Link
          to={`/products/${product.id}`}
          className="text-decoration-none"
          onClick={() => {
            window.scrollTo(0, 0);
          }}
        >
          <Card.Title as="div">
            <strong>{product.name}</strong>
          </Card.Title>
        </Link>
        <Card.Text as="div">
          <div className="my-3">
            <Rating
              value={product.rating}
              text={`${product.numReviews} reviews`}
              color={"#f8e825"}
            />
          </div>
        </Card.Text>
        <Card.Text as="h3">{formatVND(product.price)}</Card.Text>
      </Card.Body>
    </Card>
  );
}

export default Product;
