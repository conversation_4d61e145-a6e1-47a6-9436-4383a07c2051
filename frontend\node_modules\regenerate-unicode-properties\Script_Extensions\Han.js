const set = require('regenerate')(0x3030, 0x30FB, 0x32FF);
set.addRange(0x2E80, 0x2E99).addRange(0x2E9B, 0x2EF3).addRange(0x2F00, 0x2FD5).addRange(0x3001, 0x3003).addRange(0x3005, 0x3011).addRange(0x3013, 0x301F).addRange(0x3021, 0x302D).addRange(0x3037, 0x303F).addRange(0x3190, 0x319F).addRange(0x31C0, 0x31E3).addRange(0x3220, 0x3247).addRange(0x3280, 0x32B0).addRange(0x32C0, 0x32CB).addRange(0x3358, 0x3370).addRange(0x337B, 0x337F).addRange(0x33E0, 0x33FE).addRange(0x3400, 0x4DBF).addRange(0x4E00, 0x9FFF).addRange(0xA700, 0xA707).addRange(0xF900, 0xFA6D).addRange(0xFA70, 0xFAD9).addRange(0xFE45, 0xFE46).addRange(0xFF61, 0xFF65).addRange(0x16FE2, 0x16FE3).addRange(0x16FF0, 0x16FF1).addRange(0x1D360, 0x1D371).addRange(0x1F250, 0x1F251).addRange(0x20000, 0x2A6DF).addRange(0x2A700, 0x2B739).addRange(0x2B740, 0x2B81D).addRange(0x2B820, 0x2CEA1).addRange(0x2CEB0, 0x2EBE0).addRange(0x2F800, 0x2FA1D).addRange(0x30000, 0x3134A).addRange(0x31350, 0x323AF);
exports.characters = set;
