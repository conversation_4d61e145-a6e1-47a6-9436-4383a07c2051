from django.urls import path
from rest_framework.routers import DefaultRouter

from api.views import (
    BrandViewSet, CategoryViewSet, OrderViewSet, ProductViewSet,
    ReviewView, ReviewViewSet, StripePaymentView, WishlistViewSet,
    placeOrder, updateOrderToPaid, update_review, toggle_wishlist, check_wishlist_status,
    check_review_permission
)

router = DefaultRouter()
router.register('brands', BrandViewSet, basename='brands')
router.register('category', CategoryViewSet, basename='category')
router.register('products', ProductViewSet, basename='products')
router.register('orders', OrderViewSet, basename='orders')
router.register('reviews', ReviewViewSet, basename='reviews')
router.register('wishlist', WishlistViewSet, basename='wishlist')

urlpatterns = [*router.urls,
    path('placeorder/', placeOrder, name='create-order'),
    path('orders/<str:pk>/pay/', updateOrderToPaid, name="pay"),
    path('stripe-payment/', StripePaymentView.as_view(),
        name='stipe-payment'),
    path('products/<str:pk>/reviews/', ReviewView.as_view(), name='product-reviews'),
    path('products/<str:pk>/reviews/<str:review_id>/', update_review, name='update-review'),
    path('products/<str:pk>/review-permission/', check_review_permission, name='check-review-permission'),
    path('products/<int:product_id>/wishlist/toggle/', toggle_wishlist, name='toggle-wishlist'),
    path('products/<int:product_id>/wishlist/status/', check_wishlist_status, name='check-wishlist-status'),
]


