"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return _arrayWithoutHoles;
    }
});
var _arrayLikeToArrayMjs = /*#__PURE__*/ _interopRequireDefault(require("./_array_like_to_array.js"));
function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _arrayWithoutHoles(arr) {
    if (Array.isArray(arr)) return (0, _arrayLikeToArrayMjs.default)(arr);
}
