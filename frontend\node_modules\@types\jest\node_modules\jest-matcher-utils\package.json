{"name": "jest-matcher-utils", "description": "A set of utility functions for expect and related packages", "version": "29.4.3", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/jest-matcher-utils"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"chalk": "^4.0.0", "jest-diff": "^29.4.3", "jest-get-type": "^29.4.3", "pretty-format": "^29.4.3"}, "devDependencies": {"@jest/test-utils": "^29.4.3", "@types/node": "*"}, "publishConfig": {"access": "public"}, "gitHead": "a49c88610e49a3242576160740a32a2fe11161e1"}