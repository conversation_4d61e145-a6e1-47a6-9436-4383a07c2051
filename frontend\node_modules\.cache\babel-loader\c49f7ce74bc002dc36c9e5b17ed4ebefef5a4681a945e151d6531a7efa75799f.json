{"ast": null, "code": "var _jsxFileName = \"D:\\\\CHUONGTRINHHOC\\\\DATN\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\product.jsx\";\nimport React from \"react\";\nimport { Card } from \"react-bootstrap\";\nimport Rating from \"./rating\";\nimport WishlistButton from \"./wishlistButton\";\nimport { Link } from \"react-router-dom\";\nimport { formatVND } from \"../utils/currency\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Product(_ref) {\n  let {\n    product\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: \"my-3 p-3 rounded\",\n    children: [/*#__PURE__*/_jsxDEV(Link, {\n      to: `/products/${product.id}`,\n      onClick: () => {\n        window.scrollTo(0, 0);\n      },\n      children: /*#__PURE__*/_jsxDEV(Card.Img, {\n        src: product.image\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: `/products/${product.id}`,\n        className: \"text-decoration-none\",\n        onClick: () => {\n          window.scrollTo(0, 0);\n        },\n        children: /*#__PURE__*/_jsxDEV(Card.Title, {\n          as: \"div\",\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: product.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card.Text, {\n        as: \"div\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-3\",\n          children: /*#__PURE__*/_jsxDEV(Rating, {\n            value: product.rating,\n            text: `${product.numReviews} reviews`,\n            color: \"#f8e825\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card.Text, {\n        as: \"h3\",\n        children: formatVND(product.price)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n}\n_c = Product;\nexport default Product;\nvar _c;\n$RefreshReg$(_c, \"Product\");", "map": {"version": 3, "names": ["React", "Card", "Rating", "WishlistButton", "Link", "formatVND", "jsxDEV", "_jsxDEV", "Product", "_ref", "product", "className", "children", "to", "id", "onClick", "window", "scrollTo", "Img", "src", "image", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "Title", "as", "name", "Text", "value", "rating", "text", "numReviews", "color", "price", "_c", "$RefreshReg$"], "sources": ["D:/CHUONGTRINHHOC/DATN/Python-KienTap-/frontend/src/components/product.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Card } from \"react-bootstrap\";\r\nimport Rating from \"./rating\";\r\nimport WishlistButton from \"./wishlistButton\";\r\nimport { Link } from \"react-router-dom\";\r\nimport { formatVND } from \"../utils/currency\";\r\n\r\nfunction Product({ product }) {\r\n  return (\r\n    <Card className=\"my-3 p-3 rounded\">\r\n      <Link\r\n        to={`/products/${product.id}`}\r\n        onClick={() => {\r\n          window.scrollTo(0, 0);\r\n        }}\r\n      >\r\n        <Card.Img src={product.image} />\r\n      </Link>\r\n      <Card.Body>\r\n        <Link\r\n          to={`/products/${product.id}`}\r\n          className=\"text-decoration-none\"\r\n          onClick={() => {\r\n            window.scrollTo(0, 0);\r\n          }}\r\n        >\r\n          <Card.Title as=\"div\">\r\n            <strong>{product.name}</strong>\r\n          </Card.Title>\r\n        </Link>\r\n        <Card.Text as=\"div\">\r\n          <div className=\"my-3\">\r\n            <Rating\r\n              value={product.rating}\r\n              text={`${product.numReviews} reviews`}\r\n              color={\"#f8e825\"}\r\n            />\r\n          </div>\r\n        </Card.Text>\r\n        <Card.Text as=\"h3\">{formatVND(product.price)}</Card.Text>\r\n      </Card.Body>\r\n    </Card>\r\n  );\r\n}\r\n\r\nexport default Product;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,iBAAiB;AACtC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,SAAS,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,SAASC,OAAOA,CAAAC,IAAA,EAAc;EAAA,IAAb;IAAEC;EAAQ,CAAC,GAAAD,IAAA;EAC1B,oBACEF,OAAA,CAACN,IAAI;IAACU,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAChCL,OAAA,CAACH,IAAI;MACHS,EAAE,EAAG,aAAYH,OAAO,CAACI,EAAG,EAAE;MAC9BC,OAAO,EAAEA,CAAA,KAAM;QACbC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;MACvB,CAAE;MAAAL,QAAA,eAEFL,OAAA,CAACN,IAAI,CAACiB,GAAG;QAACC,GAAG,EAAET,OAAO,CAACU;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAG;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAC3B,eACPjB,OAAA,CAACN,IAAI,CAACwB,IAAI;MAAAb,QAAA,gBACRL,OAAA,CAACH,IAAI;QACHS,EAAE,EAAG,aAAYH,OAAO,CAACI,EAAG,EAAE;QAC9BH,SAAS,EAAC,sBAAsB;QAChCI,OAAO,EAAEA,CAAA,KAAM;UACbC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;QACvB,CAAE;QAAAL,QAAA,eAEFL,OAAA,CAACN,IAAI,CAACyB,KAAK;UAACC,EAAE,EAAC,KAAK;UAAAf,QAAA,eAClBL,OAAA;YAAAK,QAAA,EAASF,OAAO,CAACkB;UAAI;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAU;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACpB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACR,eACPjB,OAAA,CAACN,IAAI,CAAC4B,IAAI;QAACF,EAAE,EAAC,KAAK;QAAAf,QAAA,eACjBL,OAAA;UAAKI,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBL,OAAA,CAACL,MAAM;YACL4B,KAAK,EAAEpB,OAAO,CAACqB,MAAO;YACtBC,IAAI,EAAG,GAAEtB,OAAO,CAACuB,UAAW,UAAU;YACtCC,KAAK,EAAE;UAAU;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACI,eACZjB,OAAA,CAACN,IAAI,CAAC4B,IAAI;QAACF,EAAE,EAAC,IAAI;QAAAf,QAAA,EAAEP,SAAS,CAACK,OAAO,CAACyB,KAAK;MAAC;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAa;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAC/C;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACP;AAEX;AAACY,EAAA,GApCQ5B,OAAO;AAsChB,eAAeA,OAAO;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}