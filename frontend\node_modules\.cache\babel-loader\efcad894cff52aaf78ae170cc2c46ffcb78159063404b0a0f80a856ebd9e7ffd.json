{"ast": null, "code": "var _jsxFileName = \"D:\\\\CHUONGTRINHHOC\\\\DATN\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\stripePaymentWrapper.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Elements } from '@stripe/react-stripe-js';\nimport { loadStripe } from '@stripe/stripe-js';\nimport PaymentForm from './paymentForm';\nimport httpService from '../services/httpService';\nimport Message from './message';\n\n// Cập nhật publishable key\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst stripePromise = loadStripe(\"pk_test_51ReEgVQ2LebguEikZZvEcNiJBZmUvDacROHFO9rj3fzmtxVidakoHyGrtUG97bNg2rxfJ7ZAXQ70xhp7PwUboGRF004h4qIWi4\");\nexport default function StripePaymentWrapper(_ref) {\n  _s();\n  let {\n    id\n  } = _ref;\n  const [clientSecret, setClientSecret] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const requestSentRef = useRef(false);\n  useEffect(() => {\n    const createPaymentIntent = async () => {\n      // Nếu đã gửi request hoặc đã có clientSecret, không gửi lại\n      if (requestSentRef.current || clientSecret) {\n        return;\n      }\n\n      // Đánh dấu đã gửi request\n      requestSentRef.current = true;\n      try {\n        setLoading(true);\n        console.log(\"Creating payment intent for order:\", id);\n        const {\n          data\n        } = await httpService.post(\"/api/stripe-payment/\", {\n          order: id\n        });\n        if (data.clientSecret) {\n          console.log(\"Received client secret successfully\");\n          setClientSecret(data.clientSecret);\n        } else {\n          console.error(\"No client secret in response:\", data);\n          setError(\"Failed to initialize payment form\");\n        }\n      } catch (ex) {\n        var _ex$response, _ex$response2, _ex$response2$data;\n        console.error(\"Error creating payment intent:\", ((_ex$response = ex.response) === null || _ex$response === void 0 ? void 0 : _ex$response.data) || ex.message);\n        setError(\"Could not initialize payment: \" + (((_ex$response2 = ex.response) === null || _ex$response2 === void 0 ? void 0 : (_ex$response2$data = _ex$response2.data) === null || _ex$response2$data === void 0 ? void 0 : _ex$response2$data.error) || ex.message));\n        // Reset flag nếu có lỗi để có thể thử lại\n        requestSentRef.current = false;\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (id) {\n      createPaymentIntent();\n    }\n  }, [id, clientSecret]);\n  const appearance = {\n    theme: \"stripe\",\n    variables: {\n      colorPrimary: '#0570de'\n    }\n  };\n  const options = clientSecret ? {\n    clientSecret,\n    appearance\n  } : {};\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"Loading payment form...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(Message, {\n    variant: \"danger\",\n    children: error\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 21\n  }, this);\n  if (!clientSecret) return /*#__PURE__*/_jsxDEV(Message, {\n    variant: \"danger\",\n    children: \"Could not initialize payment form\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 29\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"payment\",\n    children: /*#__PURE__*/_jsxDEV(Elements, {\n      options: options,\n      stripe: stripePromise,\n      children: /*#__PURE__*/_jsxDEV(PaymentForm, {\n        id: id\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 5\n  }, this);\n}\n_s(StripePaymentWrapper, \"+qIEXbJhR1nGijzmfXYTNf77slQ=\");\n_c = StripePaymentWrapper;\nvar _c;\n$RefreshReg$(_c, \"StripePaymentWrapper\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Elements", "loadStripe", "PaymentForm", "httpService", "Message", "jsxDEV", "_jsxDEV", "stripePromise", "StripePaymentWrapper", "_ref", "_s", "id", "clientSecret", "setClientSecret", "loading", "setLoading", "error", "setError", "requestSentRef", "createPaymentIntent", "current", "console", "log", "data", "post", "order", "ex", "_ex$response", "_ex$response2", "_ex$response2$data", "response", "message", "appearance", "theme", "variables", "colorPrimary", "options", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "className", "stripe", "_c", "$RefreshReg$"], "sources": ["D:/CHUONGTRINHHOC/DATN/Python-KienTap-/frontend/src/components/stripePaymentWrapper.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\nimport { Elements } from '@stripe/react-stripe-js';\r\nimport { loadStripe } from '@stripe/stripe-js';\r\nimport PaymentForm from './paymentForm';\r\nimport httpService from '../services/httpService';\r\nimport Message from './message';\r\n\r\n// Cập nhật publishable key\r\nconst stripePromise = loadStripe(\r\n    \"pk_test_51ReEgVQ2LebguEikZZvEcNiJBZmUvDacROHFO9rj3fzmtxVidakoHyGrtUG97bNg2rxfJ7ZAXQ70xhp7PwUboGRF004h4qIWi4\"\r\n\r\n);\r\n\r\nexport default function StripePaymentWrapper({ id }) {\r\n  const [clientSecret, setClientSecret] = useState('');\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const requestSentRef = useRef(false);\r\n\r\n  useEffect(() => {\r\n    const createPaymentIntent = async () => {\r\n      // Nếu đã gửi request hoặc đã có clientSecret, không gửi lại\r\n      if (requestSentRef.current || clientSecret) {\r\n        return;\r\n      }\r\n      \r\n      // Đánh dấu đã gửi request\r\n      requestSentRef.current = true;\r\n      \r\n      try {\r\n        setLoading(true);\r\n        console.log(\"Creating payment intent for order:\", id);\r\n        \r\n        const { data } = await httpService.post(\"/api/stripe-payment/\", {order: id});\r\n        \r\n        if (data.clientSecret) {\r\n          console.log(\"Received client secret successfully\");\r\n          setClientSecret(data.clientSecret);\r\n        } else {\r\n          console.error(\"No client secret in response:\", data);\r\n          setError(\"Failed to initialize payment form\");\r\n        }\r\n      } catch (ex) {\r\n        console.error(\"Error creating payment intent:\", ex.response?.data || ex.message);\r\n        setError(\"Could not initialize payment: \" + (ex.response?.data?.error || ex.message));\r\n        // Reset flag nếu có lỗi để có thể thử lại\r\n        requestSentRef.current = false;\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    \r\n    if (id) {\r\n      createPaymentIntent();\r\n    }\r\n  }, [id, clientSecret]);\r\n\r\n  const appearance = {\r\n    theme: \"stripe\",\r\n    variables: {\r\n      colorPrimary: '#0570de',\r\n    }\r\n  };\r\n\r\n  const options = clientSecret ? {\r\n    clientSecret,\r\n    appearance,\r\n  } : {};\r\n\r\n  if (loading) return <div>Loading payment form...</div>;\r\n  if (error) return <Message variant=\"danger\">{error}</Message>;\r\n  if (!clientSecret) return <Message variant=\"danger\">Could not initialize payment form</Message>;\r\n\r\n  return (\r\n    <div className=\"payment\">\r\n      <Elements options={options} stripe={stripePromise}>\r\n        <PaymentForm id={id} />\r\n      </Elements>\r\n    </div>\r\n  );\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,OAAO,MAAM,WAAW;;AAE/B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,aAAa,GAAGN,UAAU,CAC5B,6GAA6G,CAEhH;AAED,eAAe,SAASO,oBAAoBA,CAAAC,IAAA,EAAS;EAAAC,EAAA;EAAA,IAAR;IAAEC;EAAG,CAAC,GAAAF,IAAA;EACjD,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMqB,cAAc,GAAGnB,MAAM,CAAC,KAAK,CAAC;EAEpCD,SAAS,CAAC,MAAM;IACd,MAAMqB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;MACtC;MACA,IAAID,cAAc,CAACE,OAAO,IAAIR,YAAY,EAAE;QAC1C;MACF;;MAEA;MACAM,cAAc,CAACE,OAAO,GAAG,IAAI;MAE7B,IAAI;QACFL,UAAU,CAAC,IAAI,CAAC;QAChBM,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEX,EAAE,CAAC;QAErD,MAAM;UAAEY;QAAK,CAAC,GAAG,MAAMpB,WAAW,CAACqB,IAAI,CAAC,sBAAsB,EAAE;UAACC,KAAK,EAAEd;QAAE,CAAC,CAAC;QAE5E,IAAIY,IAAI,CAACX,YAAY,EAAE;UACrBS,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;UAClDT,eAAe,CAACU,IAAI,CAACX,YAAY,CAAC;QACpC,CAAC,MAAM;UACLS,OAAO,CAACL,KAAK,CAAC,+BAA+B,EAAEO,IAAI,CAAC;UACpDN,QAAQ,CAAC,mCAAmC,CAAC;QAC/C;MACF,CAAC,CAAC,OAAOS,EAAE,EAAE;QAAA,IAAAC,YAAA,EAAAC,aAAA,EAAAC,kBAAA;QACXR,OAAO,CAACL,KAAK,CAAC,gCAAgC,EAAE,EAAAW,YAAA,GAAAD,EAAE,CAACI,QAAQ,cAAAH,YAAA,uBAAXA,YAAA,CAAaJ,IAAI,KAAIG,EAAE,CAACK,OAAO,CAAC;QAChFd,QAAQ,CAAC,gCAAgC,IAAI,EAAAW,aAAA,GAAAF,EAAE,CAACI,QAAQ,cAAAF,aAAA,wBAAAC,kBAAA,GAAXD,aAAA,CAAaL,IAAI,cAAAM,kBAAA,uBAAjBA,kBAAA,CAAmBb,KAAK,KAAIU,EAAE,CAACK,OAAO,CAAC,CAAC;QACrF;QACAb,cAAc,CAACE,OAAO,GAAG,KAAK;MAChC,CAAC,SAAS;QACRL,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIJ,EAAE,EAAE;MACNQ,mBAAmB,EAAE;IACvB;EACF,CAAC,EAAE,CAACR,EAAE,EAAEC,YAAY,CAAC,CAAC;EAEtB,MAAMoB,UAAU,GAAG;IACjBC,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE;MACTC,YAAY,EAAE;IAChB;EACF,CAAC;EAED,MAAMC,OAAO,GAAGxB,YAAY,GAAG;IAC7BA,YAAY;IACZoB;EACF,CAAC,GAAG,CAAC,CAAC;EAEN,IAAIlB,OAAO,EAAE,oBAAOR,OAAA;IAAA+B,QAAA,EAAK;EAAuB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QAAM;EACtD,IAAIzB,KAAK,EAAE,oBAAOV,OAAA,CAACF,OAAO;IAACsC,OAAO,EAAC,QAAQ;IAAAL,QAAA,EAAErB;EAAK;IAAAsB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QAAW;EAC7D,IAAI,CAAC7B,YAAY,EAAE,oBAAON,OAAA,CAACF,OAAO;IAACsC,OAAO,EAAC,QAAQ;IAAAL,QAAA,EAAC;EAAiC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QAAU;EAE/F,oBACEnC,OAAA;IAAKqC,SAAS,EAAC,SAAS;IAAAN,QAAA,eACtB/B,OAAA,CAACN,QAAQ;MAACoC,OAAO,EAAEA,OAAQ;MAACQ,MAAM,EAAErC,aAAc;MAAA8B,QAAA,eAChD/B,OAAA,CAACJ,WAAW;QAACS,EAAE,EAAEA;MAAG;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAG;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EACd;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACP;AAEV;AAAC/B,EAAA,CAnEuBF,oBAAoB;AAAAqC,EAAA,GAApBrC,oBAAoB;AAAA,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}