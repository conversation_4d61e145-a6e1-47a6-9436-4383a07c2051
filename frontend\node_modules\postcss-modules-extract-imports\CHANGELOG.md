# Change Log

All notable changes to this project will be documented in this file.
This project adheres to [Semantic Versioning](http://semver.org/).

## [3.0.0](https://github.com/postcss-modules-local-by-default/compare/v3.0.0-rc.3...v3.0.0) - 2020-10-13

### Fixes

- compatibility with plugins other plugins

## [3.0.0-rc.3](https://github.com/postcss-modules-local-by-default/compare/v3.0.0-rc.2...v3.0.0-rc.3) - 2020-10-11

### Fixes

- broken release

## [3.0.0-rc.2](https://github.com/postcss-modules-local-by-default/compare/v3.0.0-rc.1...v3.0.0-rc.2) - 2020-10-08

### BREAKING CHANGE

- minimum supported `postcss` version is `^8.1.0`

### Fixes

- minimum supported `Node.js` version is `^10 || ^12 || >= 14`
- compatibility with other plugins
- compatibility with PostCSS 8

## [3.0.0-rc.1](https://github.com/postcss-modules-local-by-default/compare/v3.0.0-rc.0...v3.0.0-rc.1) - 2020-09-18

### Fixes

- avoid using `postcss` directly for new rules and decls

## [3.0.0-rc.0](https://github.com/postcss-modules-local-by-default/compare/2.0.0...v3.0.0-rc.0) - 2020-09-18

### BREAKING CHANGE

- minimum supported `Node.js` version is `>= 10.13.0 || >= 12.13.0 || >= 14`
- minimum supported `postcss` version is `^8.0.3`
- `postcss` was moved to `peerDependencies`, you need to install `postcss` in your project before use the plugin
